<script setup>
import ProjectLogo from '../ProjectLogo/ProjectLogo.vue';
import ControlButton from '../ControlButton/ControlButton.vue';
import GoToFavoriteBtn from '../GoToFavoriteBtn/GoToFavoriteBtn.vue';
// Import EnquiryButton from '../EnquiryButton/EnquiryButton.vue';
import FavoriteView from '../FavoriteView/FavoriteView.vue';
import UnitPlanFavoriteView from '../UnitPlanFavoriteView/UnitPlanFavoriteView.vue';
import { ref, onMounted, onUnmounted, defineEmits, defineProps, onUpdated, computed, watch } from 'vue';
import { getCookie, getSplashCookie, Googleanalytics } from '../../../helpers/helper';
import { useRoute } from 'vue-router';
import { creationToolStore } from '../../../store/index' ;
import router from '../../../router/index';
import DropDownLanguage from '../DropDownLanguage/DropDownLanguage.vue';
import CurrencyDropDown from '../CurrencyDropdown/CurrencyDropDown.vue';
import ShareModal from '../ShareModal/ShareModal.vue';
const route = useRoute();
const Store = creationToolStore();

var showTooltip = ref([]),  checkAutoExit = ref(false);

const props=defineProps({
  isRoot: {
    type: Boolean,
    default () {
      return false;
    },
  },
  showLogo: {
    type: Boolean,
    default () {
      return true;
    },
  },
  isOrganization: {
    type: Boolean,
    default () {
      return false;
    },
  },
  favCloseClicked: {
    type: Boolean,
    default () {
      return false;
    },
  },
});
var isRootcheck = computed(() => {
  return props.isRoot;
});

Store.getListOfBuildings(route.params.projectId, route.params.organizationId);
Store.getCommunities(route.params.projectId, route.params.organizationId);

const navbarRef = ref(null);
const navbarRefMobile = ref(null);
const showShareModal = ref(false);

const updateNavbarHeight = () => {
  let height = 0;
  if (navbarRef.value) {
    height = navbarRef.value.offsetHeight;
  } else if (navbarRefMobile.value) {
    height = navbarRefMobile.value.offsetHeight;
  }
  if (height) {
    document.documentElement.style.setProperty('--navbar-height', `${height}px`);
  }
};

onMounted(() => {
  updateNavbarHeight();
  window.addEventListener('resize', updateNavbarHeight);
});

onUpdated(() => {
  updateNavbarHeight();
});

onUnmounted(() => {
  window.removeEventListener('resize', updateNavbarHeight);
});

const showUnitplanCard = ref(false);
const emit = defineEmits(['gotoFavourtiesNav', 'handleNavigation', 'toggle']);
const noAddFavoritesToolTip = ref(false);
const openFav = ref(false), isAndriod = ref();
const isUntiPlanFavoritesOpen = ref(false);
const toolBtnData=ref({
  fullscreenActiveSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8 3.09779C8 3.09779 4.03374 2.74194 3.38783 3.38785C2.74191 4.03375 3.09783 8 3.09783 8" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8 20.9022C8 20.9022 4.03374 21.2581 3.38783 20.6122C2.74191 19.9662 3.09783 16 3.09783 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 3.09779C16 3.09779 19.9663 2.74194 20.6122 3.38785C21.2581 4.03375 20.9022 8 20.9022 8" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16 20.9022C16 20.9022 19.9663 21.2581 20.6122 20.6122C21.2581 19.9662 20.9022 16 20.9022 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M14.0107 9.99847L20.0625 3.94678" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.99695 14.0024L3.63965 20.3807" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M9.99732 10.0024L3.8457 3.85889" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M13.9795 14.0024L20.5279 20.4983" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  fullscreenInactiveSVG: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M14.2299 17.9947C14.2194 17.2447 13.7042 14.7612 14.2307 14.2347C14.7573 13.7083 17.24 14.2247 17.9897 14.2355M20.9997 20.9981L14.6149 14.6146" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M9.7698 17.9947C9.7803 17.2447 10.2955 14.7612 9.769 14.2347C9.24247 13.7083 6.75975 14.2247 6.01005 14.2355M3 20.9981L9.38478 14.6146" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M6.00765 9.76133C6.75739 9.7709 9.24092 10.2832 9.76664 9.75585C10.2922 9.22853 9.77284 6.74581 9.76116 5.99592M9.37715 9.36743L3.00195 3.00244" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        <path d="M17.9918 9.76133C17.2421 9.7709 14.7585 10.2832 14.2328 9.75585C13.7072 9.22853 14.2266 6.74581 14.2383 5.99592M14.6223 9.36743L20.9975 3.00244" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
`,
  radiusActiveSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2V4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20C16.4 20 20 16.4 20 12H22C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2ZM12 6V8C9.8 8 8 9.8 8 12C8 14.2 9.8 16 12 16C14.2 16 16 14.2 16 12H18C18 15.3 15.3 18 12 18C8.7 18 6 15.3 6 12C6 8.7 8.7 6 12 6Z" fill="#D8DBDF"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 14C10.9 14 10 13.1 10 12C10 10.9 10.9 10 12 10C13.1 10 14 10.9 14 12C14 13.1 13.1 14 12 14Z" fill="#D8DBDF"/>
        </svg> `,
  radiusInactiveSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none"  xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2V4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20C16.4 20 20 16.4 20 12H22C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2ZM12 6V8C9.8 8 8 9.8 8 12C8 14.2 9.8 16 12 16C14.2 16 16 14.2 16 12H18C18 15.3 15.3 18 12 18C8.7 18 6 15.3 6 12C6 8.7 8.7 6 12 6Z" fill="#D8DBDF"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 14C10.9 14 10 13.1 10 12C10 10.9 10.9 10 12 10C13.1 10 14 10.9 14 12C14 13.1 13.1 14 12 14Z" fill="#D8DBDF"/>
        </svg>`,
  touchActiveSVG: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M11.4359 13.9995C11.4049 12.8509 11.4822 12.7126 11.5643 12.4571C11.6464 12.2016 12.2202 11.2802 12.4232 10.6219C13.0802 8.49197 12.468 8.03896 11.6516 7.43496C10.7463 6.76512 9.03889 6.4259 8.1921 6.49934V3.04672C8.1921 2.46863 7.72348 2 7.14539 2C6.5673 2 6.09867 2.46863 6.09867 3.04672V6.77923M6.09867 6.77923L4.98804 7.76598C4.2436 8.46977 4.09198 8.88161 4.03355 9.13083C3.92011 9.61841 4.07507 10.1457 4.79616 11.1903C5.49741 12.1545 6.0603 12.8251 6.09898 13.3919V14M6.09867 6.77923V9.21934" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>`,
  touchInactiveSVG: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.4359 13.9995C11.4049 12.8509 11.4822 12.7126 11.5643 12.4571C11.6464 12.2016 12.2202 11.2802 12.4232 10.6219C13.0802 8.49197 12.468 8.03896 11.6516 7.43496C10.7463 6.76512 9.03889 6.4259 8.1921 6.49934V3.04672C8.1921 2.46863 7.72348 2 7.14539 2C6.5673 2 6.09867 2.46863 6.09867 3.04672V6.77923M6.09867 6.77923L4.98804 7.76598C4.2436 8.46977 4.09198 8.88161 4.03355 9.13083C3.92011 9.61841 4.07507 10.1457 4.79616 11.1903C5.49741 12.1545 6.0603 12.8251 6.09898 13.3919V14M6.09867 6.77923V9.21934" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  svgVisibilityActiveSVG: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M8 11.4286C7.3671 11.4286 6.74841 11.2275 6.22218 10.8508C5.69594 10.474 5.28579 9.93855 5.04359 9.31206C4.80138 8.68557 4.73801 7.9962 4.86149 7.33112C4.98496 6.66604 5.28973 6.05513 5.73726 5.57563C6.18479 5.09614 6.75497 4.7696 7.37571 4.63731C7.99645 4.50501 8.63986 4.57291 9.22459 4.83241C9.80931 5.09191 10.3091 5.53136 10.6607 6.09519C11.0123 6.65901 11.2 7.32189 11.2 8C11.2 8.90931 10.8629 9.78138 10.2627 10.4244C9.66263 11.0673 8.84869 11.4286 8 11.4286ZM8 6.28571C7.68355 6.28571 7.37421 6.38626 7.11109 6.57462C6.84797 6.76299 6.64289 7.03073 6.52179 7.34397C6.40069 7.65722 6.36901 8.0019 6.43074 8.33444C6.49248 8.66698 6.64487 8.97244 6.86863 9.21218C7.09239 9.45193 7.37749 9.6152 7.68786 9.68135C7.99823 9.74749 8.31993 9.71354 8.61229 9.58379C8.90466 9.45404 9.15454 9.23432 9.33035 8.95241C9.50616 8.67049 9.6 8.33905 9.6 8C9.6 7.54534 9.43143 7.10931 9.13137 6.78782C8.83131 6.46633 8.42435 6.28571 8 6.28571Z" fill="white"/>
  <path d="M8 14C2.8368 14 0 9.49314 0 8C0 6.57371 3.6896 2 8 2C12.3104 2 16 6.57371 16 8C16 9.49314 13.1632 14 8 14ZM1.6104 8.024C2.17958 9.31795 3.08525 10.4084 4.21868 11.1643C5.3521 11.9203 6.66509 12.3097 8 12.2857C9.33491 12.3097 10.6479 11.9203 11.7813 11.1643C12.9147 10.4084 13.8204 9.31795 14.3896 8.024C14.0992 7.19257 11.404 3.71429 8 3.71429C4.596 3.71429 1.9008 7.19257 1.6104 8.024Z" fill="white"/>
</svg>`,
  svgVisibilityInActiveSVG: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <g clip-path="url(#clip0_8135_6096)">
    <path d="M1.6 10.9187L4.044 8.43285C4.02162 8.28947 4.00693 8.14496 4 7.99996C4.00127 6.92132 4.42311 5.88722 5.17298 5.12451C5.92285 4.36179 6.93952 3.93273 8 3.93144C8.14255 3.93822 8.28462 3.95289 8.4256 3.97538L9.8208 2.55628C9.227 2.39319 8.61505 2.30842 8 2.30403C3.6896 2.30403 0 6.64596 0 7.99996C0.208644 9.12339 0.7701 10.1476 1.6 10.9187Z" fill="#1F2A37"/>
    <path d="M10.16 6.95354L14.9656 2.06562C15.042 1.99056 15.103 1.90077 15.1449 1.80149C15.1868 1.70222 15.2089 1.59544 15.2098 1.4874C15.2107 1.37936 15.1905 1.27221 15.1503 1.17221C15.11 1.07221 15.0506 0.981355 14.9755 0.904954C14.9004 0.828553 14.8111 0.768133 14.7128 0.72722C14.6144 0.686306 14.5091 0.665718 14.4029 0.666657C14.2967 0.667596 14.1917 0.690043 14.0941 0.732688C13.9965 0.775333 13.9082 0.837323 13.8344 0.91504L9.0288 5.80296C8.87832 5.7294 8.72065 5.67213 8.5584 5.63208L8.5304 5.62313C8.13506 5.52862 7.72254 5.53836 7.33194 5.65143C6.94133 5.7645 6.58559 5.97715 6.29843 6.26924C6.01126 6.56132 5.80219 6.92316 5.69102 7.32045C5.57985 7.71775 5.57028 8.13733 5.6632 8.53944C5.6632 8.54921 5.6696 8.55735 5.6712 8.5663C5.71059 8.73196 5.76717 8.89289 5.84 9.04638L1.0344 13.9343C0.957992 14.0094 0.897046 14.0992 0.855119 14.1984C0.813192 14.2977 0.791123 14.4045 0.7902 14.5125C0.789277 14.6206 0.809518 14.7277 0.849743 14.8277C0.889968 14.9277 0.94937 15.0186 1.02448 15.095C1.0996 15.1714 1.18892 15.2318 1.28724 15.2727C1.38555 15.3136 1.4909 15.3342 1.59712 15.3333C1.70334 15.3323 1.80832 15.3099 1.90592 15.2672C2.00353 15.2246 2.0918 15.1626 2.1656 15.0849L6.9712 10.197C7.12211 10.271 7.28033 10.3286 7.4432 10.3687C7.452 10.3687 7.46 10.3743 7.4696 10.3768C7.86494 10.4713 8.27746 10.4616 8.66806 10.3485C9.05867 10.2354 9.41441 10.0228 9.70158 9.73068C9.98874 9.4386 10.1978 9.07676 10.309 8.67947C10.4201 8.28217 10.4297 7.86259 10.3368 7.46047C10.3368 7.45071 10.3304 7.44176 10.328 7.43199C10.2886 7.26696 10.2323 7.1066 10.16 6.95354Z" fill="#1F2A37"/>
    <path d="M14.2568 5.22767L11.9712 7.55242C12.0438 8.15935 11.9797 8.77513 11.7837 9.35313C11.5877 9.93112 11.2649 10.4562 10.8399 10.8885C10.4148 11.3208 9.89864 11.6491 9.33038 11.8485C8.76212 12.0478 8.15671 12.1131 7.56 12.0392L6.132 13.4916C6.7459 13.6262 7.37201 13.6946 8 13.6959C13.1624 13.6959 16 9.41743 16 7.99996C16 7.05118 14.6904 5.66137 14.2568 5.22767Z" fill="#1F2A37"/>
  </g>
  <defs>
    <clipPath id="clip0_8135_6096">
      <rect width="16" height="16" fill="white"/>
    </clipPath>
  </defs>
</svg>`,
  filterActiveSVG: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.9454 11.1866V2.49996C5.9454 2.27895 5.85997 2.06698 5.7079 1.9107C5.55584 1.75442 5.3496 1.66663 5.13455 1.66663C4.9195 1.66663 4.71325 1.75442 4.56119 1.9107C4.40913 2.06698 4.3237 2.27895 4.3237 2.49996V11.1866C3.7944 11.3597 3.3324 11.7014 3.00456 12.1622C2.67671 12.623 2.5 13.1791 2.5 13.75C2.5 14.3208 2.67671 14.8769 3.00456 15.3377C3.3324 15.7985 3.7944 16.1402 4.3237 16.3133V17.5C4.3237 17.721 4.40913 17.9329 4.56119 18.0892C4.71325 18.2455 4.9195 18.3333 5.13455 18.3333C5.3496 18.3333 5.55584 18.2455 5.7079 18.0892C5.85997 17.9329 5.9454 17.721 5.9454 17.5V16.3133C6.4747 16.1402 6.93669 15.7985 7.26454 15.3377C7.59239 14.8769 7.76909 14.3208 7.76909 13.75C7.76909 13.1791 7.59239 12.623 7.26454 12.1622C6.93669 11.7014 6.4747 11.3597 5.9454 11.1866ZM5.13455 14.7916C4.93408 14.7916 4.73812 14.7305 4.57144 14.6161C4.40476 14.5016 4.27485 14.3389 4.19814 14.1486C4.12142 13.9582 4.10135 13.7488 4.14046 13.5467C4.17957 13.3447 4.2761 13.1591 4.41785 13.0134C4.5596 12.8677 4.7402 12.7685 4.93681 12.7283C5.13342 12.6881 5.33722 12.7087 5.52242 12.7876C5.70762 12.8664 5.86592 12.9999 5.97729 13.1712C6.08866 13.3425 6.14811 13.5439 6.14811 13.75C6.14768 14.0261 6.04076 14.2908 5.85077 14.486C5.66078 14.6813 5.40323 14.7912 5.13455 14.7916Z" fill="white"/>
<path d="M17.5 13.75C17.4981 13.1795 17.3205 12.6243 16.9928 12.1638C16.6651 11.7034 16.204 11.3613 15.6756 11.1866V2.49996C15.6756 2.27895 15.5902 2.06698 15.4381 1.9107C15.286 1.75442 15.0798 1.66663 14.8647 1.66663C14.6497 1.66663 14.4434 1.75442 14.2914 1.9107C14.1393 2.06698 14.0539 2.27895 14.0539 2.49996V11.1866C13.5246 11.3597 13.0626 11.7014 12.7347 12.1622C12.4069 12.623 12.2302 13.1791 12.2302 13.75C12.2302 14.3208 12.4069 14.8769 12.7347 15.3377C13.0626 15.7985 13.5246 16.1402 14.0539 16.3133V17.5C14.0539 17.721 14.1393 17.9329 14.2914 18.0892C14.4434 18.2455 14.6497 18.3333 14.8647 18.3333C15.0798 18.3333 15.286 18.2455 15.4381 18.0892C15.5902 17.9329 15.6756 17.721 15.6756 17.5V16.3133C16.204 16.1386 16.6651 15.7966 16.9928 15.3361C17.3205 14.8757 17.4981 14.3204 17.5 13.75ZM14.8647 14.7916C14.6643 14.7916 14.4683 14.7305 14.3016 14.6161C14.135 14.5016 14.005 14.3389 13.9283 14.1486C13.8516 13.9582 13.8315 13.7488 13.8707 13.5467C13.9098 13.3447 14.0063 13.1591 14.148 13.0134C14.2898 12.8677 14.4704 12.7685 14.667 12.7283C14.8636 12.6881 15.0674 12.7087 15.2526 12.7876C15.4378 12.8664 15.5961 12.9999 15.7075 13.1712C15.8189 13.3425 15.8783 13.5439 15.8783 13.75C15.8779 14.0261 15.7709 14.2908 15.581 14.486C15.391 14.6813 15.1334 14.7912 14.8647 14.7916Z" fill="white"/>
<path d="M12.6349 6.24996C12.633 5.67948 12.4554 5.12426 12.1277 4.66381C11.8 4.20336 11.3389 3.8613 10.8105 3.68663V2.49996C10.8105 2.27895 10.7251 2.06698 10.573 1.9107C10.4209 1.75442 10.2147 1.66663 9.99964 1.66663C9.78459 1.66663 9.57835 1.75442 9.42629 1.9107C9.27422 2.06698 9.18879 2.27895 9.18879 2.49996V3.68663C8.65949 3.85974 8.1975 4.20141 7.86965 4.66223C7.5418 5.12304 7.3651 5.6791 7.3651 6.24996C7.3651 6.82082 7.5418 7.37687 7.86965 7.83769C8.1975 8.2985 8.65949 8.64018 9.18879 8.81329V17.5C9.18879 17.721 9.27422 17.9329 9.42629 18.0892C9.57835 18.2455 9.78459 18.3333 9.99964 18.3333C10.2147 18.3333 10.4209 18.2455 10.573 18.0892C10.7251 17.9329 10.8105 17.721 10.8105 17.5V8.81329C11.3389 8.63862 11.8 8.29656 12.1277 7.83611C12.4554 7.37566 12.633 6.82044 12.6349 6.24996ZM9.99964 7.29163C9.79918 7.29163 9.60322 7.23053 9.43654 7.11607C9.26986 7.00161 9.13995 6.83893 9.06323 6.64859C8.98652 6.45825 8.96645 6.2488 9.00556 6.04674C9.04466 5.84468 9.1412 5.65907 9.28295 5.51339C9.4247 5.36771 9.60529 5.2685 9.80191 5.22831C9.99852 5.18812 10.2023 5.20874 10.3875 5.28758C10.5727 5.36643 10.731 5.49994 10.8424 5.67124C10.9538 5.84254 11.0132 6.04394 11.0132 6.24996C11.0128 6.52609 10.9059 6.79079 10.7159 6.98604C10.5259 7.1813 10.2683 7.29119 9.99964 7.29163Z" fill="white"/>
</svg>`,
  filterInactiveSVG: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M5.9454 11.1866V2.49996C5.9454 2.27895 5.85997 2.06698 5.7079 1.9107C5.55584 1.75442 5.3496 1.66663 5.13455 1.66663C4.9195 1.66663 4.71325 1.75442 4.56119 1.9107C4.40913 2.06698 4.3237 2.27895 4.3237 2.49996V11.1866C3.7944 11.3597 3.3324 11.7014 3.00456 12.1622C2.67671 12.623 2.5 13.1791 2.5 13.75C2.5 14.3208 2.67671 14.8769 3.00456 15.3377C3.3324 15.7985 3.7944 16.1402 4.3237 16.3133V17.5C4.3237 17.721 4.40913 17.9329 4.56119 18.0892C4.71325 18.2455 4.9195 18.3333 5.13455 18.3333C5.3496 18.3333 5.55584 18.2455 5.7079 18.0892C5.85997 17.9329 5.9454 17.721 5.9454 17.5V16.3133C6.4747 16.1402 6.93669 15.7985 7.26454 15.3377C7.59239 14.8769 7.76909 14.3208 7.76909 13.75C7.76909 13.1791 7.59239 12.623 7.26454 12.1622C6.93669 11.7014 6.4747 11.3597 5.9454 11.1866ZM5.13455 14.7916C4.93408 14.7916 4.73812 14.7305 4.57144 14.6161C4.40476 14.5016 4.27485 14.3389 4.19814 14.1486C4.12142 13.9582 4.10135 13.7488 4.14046 13.5467C4.17957 13.3447 4.2761 13.1591 4.41785 13.0134C4.5596 12.8677 4.7402 12.7685 4.93681 12.7283C5.13342 12.6881 5.33722 12.7087 5.52242 12.7876C5.70762 12.8664 5.86592 12.9999 5.97729 13.1712C6.08866 13.3425 6.14811 13.5439 6.14811 13.75C6.14768 14.0261 6.04076 14.2908 5.85077 14.486C5.66078 14.6813 5.40323 14.7912 5.13455 14.7916Z" fill="white"/>
<path d="M17.5 13.75C17.4981 13.1795 17.3205 12.6243 16.9928 12.1638C16.6651 11.7034 16.204 11.3613 15.6756 11.1866V2.49996C15.6756 2.27895 15.5902 2.06698 15.4381 1.9107C15.286 1.75442 15.0798 1.66663 14.8647 1.66663C14.6497 1.66663 14.4434 1.75442 14.2914 1.9107C14.1393 2.06698 14.0539 2.27895 14.0539 2.49996V11.1866C13.5246 11.3597 13.0626 11.7014 12.7347 12.1622C12.4069 12.623 12.2302 13.1791 12.2302 13.75C12.2302 14.3208 12.4069 14.8769 12.7347 15.3377C13.0626 15.7985 13.5246 16.1402 14.0539 16.3133V17.5C14.0539 17.721 14.1393 17.9329 14.2914 18.0892C14.4434 18.2455 14.6497 18.3333 14.8647 18.3333C15.0798 18.3333 15.286 18.2455 15.4381 18.0892C15.5902 17.9329 15.6756 17.721 15.6756 17.5V16.3133C16.204 16.1386 16.6651 15.7966 16.9928 15.3361C17.3205 14.8757 17.4981 14.3204 17.5 13.75ZM14.8647 14.7916C14.6643 14.7916 14.4683 14.7305 14.3016 14.6161C14.135 14.5016 14.005 14.3389 13.9283 14.1486C13.8516 13.9582 13.8315 13.7488 13.8707 13.5467C13.9098 13.3447 14.0063 13.1591 14.148 13.0134C14.2898 12.8677 14.4704 12.7685 14.667 12.7283C14.8636 12.6881 15.0674 12.7087 15.2526 12.7876C15.4378 12.8664 15.5961 12.9999 15.7075 13.1712C15.8189 13.3425 15.8783 13.5439 15.8783 13.75C15.8779 14.0261 15.7709 14.2908 15.581 14.486C15.391 14.6813 15.1334 14.7912 14.8647 14.7916Z" fill="white"/>
<path d="M12.6349 6.24996C12.633 5.67948 12.4554 5.12426 12.1277 4.66381C11.8 4.20336 11.3389 3.8613 10.8105 3.68663V2.49996C10.8105 2.27895 10.7251 2.06698 10.573 1.9107C10.4209 1.75442 10.2147 1.66663 9.99964 1.66663C9.78459 1.66663 9.57835 1.75442 9.42629 1.9107C9.27422 2.06698 9.18879 2.27895 9.18879 2.49996V3.68663C8.65949 3.85974 8.1975 4.20141 7.86965 4.66223C7.5418 5.12304 7.3651 5.6791 7.3651 6.24996C7.3651 6.82082 7.5418 7.37687 7.86965 7.83769C8.1975 8.2985 8.65949 8.64018 9.18879 8.81329V17.5C9.18879 17.721 9.27422 17.9329 9.42629 18.0892C9.57835 18.2455 9.78459 18.3333 9.99964 18.3333C10.2147 18.3333 10.4209 18.2455 10.573 18.0892C10.7251 17.9329 10.8105 17.721 10.8105 17.5V8.81329C11.3389 8.63862 11.8 8.29656 12.1277 7.83611C12.4554 7.37566 12.633 6.82044 12.6349 6.24996ZM9.99964 7.29163C9.79918 7.29163 9.60322 7.23053 9.43654 7.11607C9.26986 7.00161 9.13995 6.83893 9.06323 6.64859C8.98652 6.45825 8.96645 6.2488 9.00556 6.04674C9.04466 5.84468 9.1412 5.65907 9.28295 5.51339C9.4247 5.36771 9.60529 5.2685 9.80191 5.22831C9.99852 5.18812 10.2023 5.20874 10.3875 5.28758C10.5727 5.36643 10.731 5.49994 10.8424 5.67124C10.9538 5.84254 11.0132 6.04394 11.0132 6.24996C11.0128 6.52609 10.9059 6.79079 10.7159 6.98604C10.5259 7.1813 10.2683 7.29119 9.99964 7.29163Z" fill="white"/>
</svg>`,
  viewActiveSVG: `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame 1707479808" clip-path="url(#clip0_126_676)">
<g id="Group 1707479723">
<path id="Ellipse 40" d="M8.15719 9.21011C8.84111 9.21011 9.34647 9.23739 9.97605 9.28848M11.4805 9.46476C13.8701 9.83837 15.5 10.5847 15.5 11.4444C15.5 12.6784 12.1421 13.6787 8 13.6787C3.85786 13.6787 0.5 12.6784 0.5 11.4444C0.5 10.8405 1.30413 10.2926 2.61078 9.89053M5.20434 9.3705C4.77536 9.42187 4.366 9.4847 3.98054 9.55774" stroke="white" stroke-width="0.364583"/>
<g id="Vector" filter="url(#filter0_d_126_676)">
<path d="M12.5031 8.14371C11.4765 8.14371 10.6413 7.30711 10.6413 6.27886V4.48133C10.7439 2.00679 14.2634 2.00869 14.3651 4.48133V6.27889C14.3651 7.30711 13.5298 8.14371 12.5031 8.14371ZM12.5031 3.57776C12.0064 3.57776 11.6022 3.98309 11.6022 4.48133V6.27889C11.6519 7.4779 13.3548 7.47696 13.404 6.27889V4.48133C13.404 3.98309 12.9999 3.57776 12.5031 3.57776ZM9.77634 6.27391C9.78297 4.87359 8.22353 3.9625 7.0136 4.65205V4.48133C7.0136 3.98312 7.41773 3.57776 7.9145 3.57776C8.41558 3.57776 8.78419 3.83617 8.78789 3.83881C9.09549 4.08105 9.56401 3.85268 9.56019 3.45758C9.56019 3.27466 9.45802 3.11566 9.30761 3.03444C9.13663 2.91988 8.61755 2.61649 7.9145 2.61649C6.88785 2.61649 6.05262 3.45306 6.05262 4.48131V6.28365C6.05262 6.28528 6.05262 6.28696 6.05265 6.28859C6.16609 8.75034 9.66386 8.74843 9.77634 6.28859C9.77634 6.28696 9.77641 6.28528 9.77641 6.28365V6.27886C9.77641 6.27723 9.77634 6.27557 9.77634 6.27391ZM7.9145 7.18244C7.41852 7.18244 7.01487 6.77842 7.0136 6.28127C7.06588 5.08541 8.7636 5.08632 8.81542 6.28127C8.81412 6.7784 8.41046 7.18244 7.9145 7.18244ZM4.61652 5.39636C4.92499 5.11414 5.1397 4.72412 5.1397 4.28543C5.1397 3.3652 4.38853 2.61654 3.46521 2.61654C2.77364 2.61654 2.28444 3.02209 2.23093 3.06832L2.23197 3.06953C2.12962 3.15767 2.06459 3.28792 2.06459 3.4336C2.06286 3.84792 2.56271 4.06728 2.86542 3.79108C2.91642 3.75225 3.16242 3.57778 3.46521 3.57778C3.85863 3.57778 4.17873 3.89524 4.17873 4.28543C4.17873 4.6125 3.75946 4.8995 3.50605 4.8995C3.24068 4.8995 3.02556 5.11468 3.02556 5.38012C3.02556 5.64557 3.24068 5.86074 3.50605 5.86074C3.57675 5.86074 3.75602 5.91991 3.93606 6.07868C4.08347 6.20865 4.17873 6.36415 4.17873 6.47479C4.17873 6.86498 3.85865 7.18244 3.46521 7.18244C3.16245 7.18244 2.91644 7.00797 2.86542 6.96913C2.56252 6.69289 2.06286 6.91237 2.06457 7.32662C2.06457 7.4723 2.1296 7.60255 2.23194 7.69069L2.23091 7.69189C2.28441 7.73813 2.77362 8.14371 3.46518 8.14371C4.38851 8.14371 5.13967 7.39502 5.13967 6.47479C5.1397 6.06838 4.92255 5.68378 4.61652 5.39636ZM6.38957 11.9647C6.36907 11.9647 6.41042 11.9673 6.38957 11.9647V11.9647ZM13.7644 2.13589C14.0962 2.13589 14.3651 1.86691 14.3651 1.53511C14.3334 0.738188 13.1953 0.738405 13.1638 1.53511C13.1638 1.86691 13.4327 2.13589 13.7644 2.13589ZM10.1361 11.862C10.1141 11.865 10.1578 11.862 10.1361 11.862V11.862Z" fill="white"/></g></g></g>
<defs>
<filter id="filter0_d_126_676" x="-0.459586" y="0.9375" width="17.3489" height="16.0764" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.52404"/>
<feGaussianBlur stdDeviation="1.26202"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_126_676"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_126_676" result="shape"/>
</filter>
<clipPath id="clip0_126_676">
<rect width="15" height="15" fill="white" transform="translate(0.5)"/>
</clipPath>
</defs>
</svg>`,
  favbuttonSVG: `<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="heart">
<path id="Vector" d="M2.44982 5.0371C2.58174 4.71862 2.7751 4.42924 3.01886 4.18548C3.26261 3.94172 3.55199 3.74837 3.87047 3.61645C4.18895 3.48452 4.5303 3.41663 4.87502 3.41663C5.21975 3.41663 5.56109 3.48452 5.87957 3.61645C6.19806 3.74837 6.48744 3.94172 6.73119 4.18548L7.50002 4.95432L8.26886 4.18548C8.76114 3.6932 9.42883 3.41663 10.125 3.41663C10.8212 3.41663 11.4889 3.6932 11.9812 4.18548C12.4735 4.67777 12.75 5.34545 12.75 6.04165C12.75 6.73785 12.4735 7.40553 11.9812 7.89782L7.50002 12.379L3.01886 7.89782C2.7751 7.65406 2.58174 7.36468 2.44982 7.0462C2.3179 6.72772 2.25 6.38637 2.25 6.04165C2.25 5.69693 2.3179 5.35558 2.44982 5.0371Z" fill="white" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></g>
</svg>`,
  favbuttonMobSVG: `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.785456 3.48159C0.973914 3.02661 1.25014 2.61321 1.59837 2.26499C1.94659 1.91677 2.35998 1.64054 2.81496 1.45208C3.26993 1.26362 3.75757 1.16663 4.25003 1.16663C4.74249 1.16663 5.23013 1.26362 5.68511 1.45208C6.14008 1.64054 6.55348 1.91677 6.9017 2.26499L8.00003 3.36333L9.09837 2.26499C9.80163 1.56173 10.7555 1.16664 11.75 1.16664C12.7446 1.16664 13.6984 1.56173 14.4017 2.26499C15.105 2.96826 15.5001 3.92209 15.5001 4.91666C15.5001 5.91123 15.105 6.86506 14.4017 7.56833L8.00003 13.97L1.59837 7.56833C1.25014 7.22011 0.973914 6.80671 0.785456 6.35173C0.596998 5.89676 0.5 5.40912 0.5 4.91666C0.5 4.4242 0.596998 3.93656 0.785456 3.48159Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
  daybuttonSVG: `<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
<path d="M9.00033 13.1667C11.3015 13.1667 13.167 11.3012 13.167 9.00002C13.167 6.69883 11.3015 4.83335 9.00033 4.83335C6.69914 4.83335 4.83366 6.69883 4.83366 9.00002C4.83366 11.3012 6.69914 13.1667 9.00033 13.1667Z" fill="#1F2A37"/>
<path d="M9.00033 4.00002C9.22134 4.00002 9.4333 3.91222 9.58958 3.75594C9.74586 3.59966 9.83366 3.3877 9.83366 3.16669V1.50002C9.83366 1.27901 9.74586 1.06704 9.58958 0.910765C9.4333 0.754484 9.22134 0.666687 9.00033 0.666687C8.77931 0.666687 8.56735 0.754484 8.41107 0.910765C8.25479 1.06704 8.16699 1.27901 8.16699 1.50002V3.16669C8.16699 3.3877 8.25479 3.59966 8.41107 3.75594C8.56735 3.91222 8.77931 4.00002 9.00033 4.00002Z" fill="#1F2A37"/>
<path d="M9.00033 14C8.77931 14 8.56735 14.0878 8.41107 14.2441C8.25479 14.4004 8.16699 14.6123 8.16699 14.8334V16.5C8.16699 16.721 8.25479 16.933 8.41107 17.0893C8.56735 17.2456 8.77931 17.3334 9.00033 17.3334C9.22134 17.3334 9.4333 17.2456 9.58958 17.0893C9.74586 16.933 9.83366 16.721 9.83366 16.5V14.8334C9.83366 14.6123 9.74586 14.4004 9.58958 14.2441C9.4333 14.0878 9.22134 14 9.00033 14Z" fill="#1F2A37"/>
<path d="M4.28616 5.46419C4.44333 5.61599 4.65383 5.69998 4.87233 5.69808C5.09082 5.69618 5.29983 5.60854 5.45434 5.45404C5.60885 5.29953 5.69649 5.09052 5.69839 4.87202C5.70029 4.65352 5.61629 4.44302 5.46449 4.28585L4.28616 3.10752C4.12899 2.95572 3.91849 2.87173 3.69999 2.87363C3.48149 2.87552 3.27248 2.96317 3.11798 3.11767C2.96347 3.27218 2.87583 3.48119 2.87393 3.69969C2.87203 3.91818 2.95603 4.12869 3.10783 4.28585L4.28616 5.46419Z" fill="#1F2A37"/>
<path d="M13.7145 12.5359C13.5573 12.3841 13.3468 12.3001 13.1283 12.302C12.9098 12.3039 12.7008 12.3915 12.5463 12.546C12.3918 12.7005 12.3042 12.9095 12.3023 13.128C12.3004 13.3465 12.3844 13.557 12.5362 13.7142L13.7145 14.8925C13.8717 15.0443 14.0822 15.1283 14.3007 15.1264C14.5192 15.1245 14.7282 15.0369 14.8827 14.8824C15.0372 14.7279 15.1248 14.5189 15.1267 14.3004C15.1286 14.0819 15.0446 13.8714 14.8928 13.7142L13.7145 12.5359Z" fill="#1F2A37"/>
<path d="M4.00033 9.00002C4.00033 8.77901 3.91253 8.56705 3.75625 8.41077C3.59997 8.25449 3.38801 8.16669 3.16699 8.16669H1.50033C1.27931 8.16669 1.06735 8.25449 0.91107 8.41077C0.75479 8.56705 0.666992 8.77901 0.666992 9.00002C0.666992 9.22103 0.75479 9.433 0.91107 9.58928C1.06735 9.74556 1.27931 9.83335 1.50033 9.83335H3.16699C3.38801 9.83335 3.59997 9.74556 3.75625 9.58928C3.91253 9.433 4.00033 9.22103 4.00033 9.00002Z" fill="#1F2A37"/>
<path d="M16.5003 8.16669H14.8337C14.6126 8.16669 14.4007 8.25449 14.2444 8.41077C14.0881 8.56705 14.0003 8.77901 14.0003 9.00002C14.0003 9.22103 14.0881 9.433 14.2444 9.58928C14.4007 9.74556 14.6126 9.83335 14.8337 9.83335H16.5003C16.7213 9.83335 16.9333 9.74556 17.0896 9.58928C17.2459 9.433 17.3337 9.22103 17.3337 9.00002C17.3337 8.77901 17.2459 8.56705 17.0896 8.41077C16.9333 8.25449 16.7213 8.16669 16.5003 8.16669Z" fill="#1F2A37"/>
<path d="M4.28616 12.5359L3.10783 13.7142C3.02823 13.7911 2.96475 13.883 2.92107 13.9847C2.8774 14.0864 2.85441 14.1957 2.85345 14.3064C2.85249 14.417 2.87357 14.5267 2.91547 14.6291C2.95738 14.7316 3.01925 14.8246 3.0975 14.9029C3.17574 14.9811 3.26878 15.043 3.3712 15.0849C3.47361 15.1268 3.58334 15.1479 3.69399 15.1469C3.80464 15.1459 3.91399 15.1229 4.01566 15.0793C4.11733 15.0356 4.20929 14.9721 4.28616 14.8925L5.46449 13.7142C5.61629 13.557 5.70029 13.3465 5.69839 13.128C5.69649 12.9095 5.60885 12.7005 5.45434 12.546C5.29983 12.3915 5.09082 12.3039 4.87233 12.302C4.65383 12.3001 4.44333 12.3841 4.28616 12.5359Z" fill="#1F2A37"/>
<path d="M13.1253 5.70835C13.3463 5.70831 13.5582 5.62048 13.7145 5.46419L14.8928 4.28585C14.9724 4.20898 15.0359 4.11703 15.0796 4.01536C15.1233 3.91369 15.1462 3.80434 15.1472 3.69369C15.1482 3.58304 15.1271 3.47331 15.0852 3.37089C15.0433 3.26848 14.9814 3.17544 14.9032 3.09719C14.8249 3.01895 14.7319 2.95707 14.6295 2.91517C14.527 2.87327 14.4173 2.85218 14.3067 2.85315C14.196 2.85411 14.0867 2.8771 13.985 2.92077C13.8833 2.96444 13.7914 3.02793 13.7145 3.10752L12.5362 4.28585C12.4197 4.4024 12.3403 4.55087 12.3082 4.71249C12.276 4.87412 12.2925 5.04165 12.3556 5.1939C12.4187 5.34615 12.5254 5.47628 12.6624 5.56786C12.7995 5.65943 12.9605 5.70832 13.1253 5.70835Z" fill="#1F2A37"/>
</svg>`,
  nightbuttonSVG: `<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
<g clip-path="url(#clip0_8811_1377)">
<path d="M11.4846 6.04613C10.7351 7.92934 10.5554 9.9914 10.9678 11.976C11.3801 13.9606 12.3665 15.7801 13.8041 17.2085C15.2418 18.6369 17.0673 19.6111 19.0539 20.01C21.0405 20.409 23.1005 20.215 24.9778 19.4523C24.0887 21.6825 22.4519 23.5338 20.3479 24.6889C18.2439 25.844 15.8037 26.2309 13.4458 25.7834C11.0878 25.3359 8.95889 24.0817 7.42396 22.236C5.88904 20.3903 5.04371 18.0679 5.03289 15.6669C5.02575 13.6023 5.63636 11.5828 6.78615 9.86822C7.93593 8.15367 9.57229 6.82254 11.4846 6.04613Z" fill="#111928"/>
</g>
<defs>
<clipPath id="clip0_8811_1377">
<rect width="20" height="20" fill="white" transform="translate(5 6)"/>
</clipPath>
</defs>
</svg>`,
  sharebuttonSVG: `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
  <path d="M17.5 8.16667L11.25 3.33333V6.25C6.875 6.25 3.33333 10 2.5 15C4.16667 12.0833 7.08333 10.8333 11.25 10.8333V13.75L17.5 8.16667Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,

  //   EnquirySVG:
  //  `<svg width="20" height="20" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">

//   EnquirySVG: `<svg width="20" height="20" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
// <path d="M1.4021 4.61879L7 7.41739L12.5979 4.61879C12.5772 4.2621 12.4209 3.92683 12.161 3.68165C11.9011 3.43646 11.5573 3.29992 11.2 3.29999H2.8C2.44271 3.29992 2.09891 3.43646 1.83902 3.68165C1.57914 3.92683 1.42282 4.2621 1.4021 4.61879V4.61879Z" fill="white"/>
// <path d="M12.6 6.18262L7.00002 8.98262L1.40002 6.18262V10.3C1.40002 10.6713 1.54752 11.0274 1.81007 11.29C2.07263 11.5525 2.42872 11.7 2.80002 11.7H11.2C11.5713 11.7 11.9274 11.5525 12.19 11.29C12.4525 11.0274 12.6 10.6713 12.6 10.3V6.18262Z" fill="white"/>
// </svg>`,
//   EnquiryMobSVG: `<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
// <path d="M2.98816 3.48816C2.67559 3.80072 2.5 4.22464 2.5 4.66667V5.5C2.5 12.4033 8.09667 18 15 18H15.8333C16.2754 18 16.6993 17.8244 17.0118 17.5118C17.3244 17.1993 17.5 16.7754 17.5 16.3333V13.6008C17.5 13.4259 17.445 13.2553 17.3427 13.1134C17.2404 12.9714 17.096 12.8653 16.93 12.81L13.1858 11.5617C12.9956 11.4984 12.7888 11.5059 12.6036 11.5827C12.4184 11.6596 12.2671 11.8006 12.1775 11.98L11.2358 13.8608C9.19538 12.9389 7.5611 11.3046 6.63917 9.26417L8.52 8.3225C8.69938 8.23288 8.84042 8.08158 8.91726 7.89637C8.9941 7.71116 9.00158 7.50445 8.93833 7.31417L7.69 3.57C7.63475 3.40413 7.52874 3.25984 7.38696 3.15754C7.24519 3.05525 7.07483 3.00013 6.9 3H4.16667C3.72464 3 3.30072 3.17559 2.98816 3.48816Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
// </svg>
// `,
});

// Back navigation
function movetoprevscene () {
  if (openFav.value) {
    openFav.value = !openFav.value;
  } else if (isUntiPlanFavoritesOpen.value) {
    isUntiPlanFavoritesOpen.value = !isUntiPlanFavoritesOpen.value;
  } else {
    emit('handleNavigation');
  }
}

function gotoFavourites (){
  noAddFavoritesToolTip.value = false;
  if (route.query.unitplan_id){
    isUntiPlanFavoritesOpen.value = !isUntiPlanFavoritesOpen.value;
  } else {
    openFav.value = !openFav.value;
  }
  Googleanalytics("favorites_clicked", {
    project_city: Store.projectCardData?.[route.params.projectId]?.city,
    project_type: Store.buildingData[route.query.building_id]?.category,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
  });
}

function removeItem (id) {
  const index = showTooltip.value.findIndex((item) => item === id);

  if (index !== -1) {
    showTooltip.value.splice(index, 1);
  }
}

function favoriteViewTooltipClickHandler (event) {
  if (event.target.id !== 'goToFavotiteText' && event.target.id !== 'goToFavotiteSvg' && event.target.id !== 'goToFavorite'){
    noAddFavoritesToolTip.value = false;
  }
}
const fullScreenChangeHandler = () => {
  // SvgLoaded.value = false;
  setTimeout(() => {
    // SvgLoaded.value = true;
  }, 1000);
  if (!document.fullscreenElement && !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
    // Emit('onClick');
    Store.isFullScreen = false;
  }
};

function onWindowResize () {
  const lowResImage = document.getElementsByClassName('svg-pan-zoom_viewport')[0];
  if (lowResImage) {
    const currentTransform = lowResImage.style.transform;
    if (currentTransform && currentTransform.startsWith('matrix')) {
      const values = currentTransform.match(/matrix\((.+)\)/)[1].split(',').map(Number);
      if (values.length === 6) {
        values[5] = 0; // Update the last value to 0
        lowResImage.style.transform = `matrix(${values.join(', ')})`;
      }
    }
  }
}

function setFullScreenCookie () {
  if (!getCookie('fullscreen')) {
    const expiryTime = new Date(Date.now() + (30 * 60000)); // 30 minutes in milliseconds
    document.cookie = `fullscreen=true; path=/; expires=${expiryTime.toUTCString()};`;
  }
}

const onClickButton = (id) => {
  if (id === 'radius'){
    router.push({ name: route.fullPath.includes('masterscene') ? 'masterScene' : 'projectScene', query: { ...route.query, [id]: route.query[id] === undefined ? true : route.query[id] === 'false' ? true : false } });
    if (route.query[id] === 'true') {
      document.getElementById("svg_highres_image").classList.remove("opacity-50");
    } else {
      document.getElementById("svg_highres_image").classList.add("opacity-50");
    }
  }
  if (id === 'fullscreen') {
    setFullScreenCookie();
    onWindowResize();
    if (!document.fullscreenElement &&
      !document.mozFullScreenElement && !document.webkitFullscreenElement && !document.msFullscreenElement) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen();
      } else if (document.documentElement.mozRequestFullScreen) {
        document.documentElement.mozRequestFullScreen();
      } else if (document.documentElement.webkitRequestFullscreen) {
        document.documentElement.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
      } else if (document.documentElement.msRequestFullscreen) {
        document.documentElement.msRequestFullscreen();
      }
      checkAutoExit.value = false;
      Store.isFullScreen = !Store.isFullScreen;
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
      checkAutoExit.value = true;
      Store.isFullScreen = !Store.isFullScreen;
    }
  }

  if (id === 'touchscreen'){
    Store.isTouchScreen=!Store.isTouchScreen;
    emit('toggle',  Store.isTouchScreen);
  }
  if (id === 'share') {
    showShareModal.value = true;
  }

  if (id === 'svgvisibility') {
    Store.svgVisibility.toggle = !Store.svgVisibility.toggle;
    Store.svgVisibility.showSVG = !Store.svgVisibility.showSVG;
  }

  if (id === 'daynight_mode') {
    const newMode = route.query.daynight_mode === 'night' ? 'day' : 'night';
    router.push({
      query: {
        ...route.query,
        daynight_mode: newMode,
      },
    });
  }
};

// Const onHandleEnquiry = () => {
// };

onMounted(() => {
  document.addEventListener('fullscreenchange', fullScreenChangeHandler);
  document.addEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.addEventListener('MSFullscreenChange', fullScreenChangeHandler);
  document.addEventListener('click', favoriteViewTooltipClickHandler);
  const splashCookie = getSplashCookie();
  if (splashCookie[route.params.projectId]) {
    Store.splashLoader = true;
  }
  isAndriod.value = /Android/i.test(navigator.userAgent);
  if (route.fullPath.includes('masterscene')){
    Store.getOrganization(route.params.organizationId);
  } else {
    Store.getListofProjects(route.params.organizationId, route.params.projectId);
  }

  // Sync day/night mode with query parameter
  if (route.query.daynight_mode) {
    Store.dayNightMode = route.query.daynight_mode === 'night';
  } else {
    // Set default to day mode if no query parameter exists
    Store.dayNightMode = false;
  }
});
onUnmounted(() => {
  document.removeEventListener('fullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('webkitfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('mozfullscreenchange', fullScreenChangeHandler);
  document.removeEventListener('MSFullscreenChange', fullScreenChangeHandler);
});
function gotoUnit (){
  const findSceneIdByUnitId = (data, unitId) => {
    for (const key in data) {
      const svgData = data[key].svgData;
      for (const svgKey in svgData) {
        const layers = svgData[svgKey].layers;
        for (const layerKey in layers) {
          const layer = layers[layerKey];
          if (layer.units && layer.units.includes(unitId)) {
            return svgData[svgKey].scene_id;
          }
        }
      }
    }
    return null; // Return null if no matching unit_id is found
  };
  const scene_id = findSceneIdByUnitId(Store.SceneData, showUnitplanCard.value);
  router.push({name: "unit.Child", params: {...route.params, sceneId: scene_id, unitplanId: Store.allUnitCardData[showUnitplanCard.value].unitplan_id}, query: {...route.query, unit_id: showUnitplanCard.value, type: 'unitplan', islocate: true, unit_name: Store.allUnitCardData[showUnitplanCard.value].name}});
  showUnitplanCard.value=false;
}

// show and hide gotoFavourites based on Available units
const hasAvailableUnits = computed(() => {
  const buildings = Object.values(Store.buildingData);
  const communities = Object.values(Store.communityData);
  return [...buildings, ...communities].some(
    (obj) => obj.unitStatus && obj.unitStatus.Available > 0,
  );
});

// Computed property to check if current scene has night mode background
const hasNightModeBackground = computed(() => {
  // Check if the current scene has night mode background (for both masterscene and regular scenes)
  if (!Store.SceneData || !route.params.sceneId) {
    return false;
  }

  const currentScene = Store.SceneData[route.params.sceneId];
  if (!currentScene || !currentScene.sceneData || !currentScene.sceneData.background) {
    return false;
  }

  return !!currentScene.sceneData.background.high_resolution_night;
});

// Watch for query parameter changes to sync with Store
watch(() => route.query.daynight_mode, (newMode) => {
  if (newMode) {
    Store.dayNightMode = newMode === 'night';
  } else {
    // Default to day mode when no query parameter is present
    Store.dayNightMode = false;
  }
});
</script>

<template>
  <div
    v-if="!Store.isMobile && !Store.isLandscape"
    ref="navbarRef"
  >
    <div class="flex justify-center items-center gap-6 px-8 py-2 absolute top-0 left-0 z-[5]">
      <!-- back navigation button -->
      <div
        v-if="!isRootcheck || openFav"
        class="w-[3.75rem] h-[3.75rem] rounded-lg bg-secondary p-[1.125rem] flex justify-center items-center cursor-pointer"
        @click="movetoprevscene()"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3.09787 11.4659C3.00053 11.7211 2.97508 12.002 3.02475 12.2729C3.07441 12.5438 3.19696 12.7927 3.37689 12.988L8.52007 18.5737C8.63868 18.7071 8.78056 18.8134 8.93743 18.8866C9.0943 18.9598 9.26303 18.9983 9.43375 18.9999C9.60448 19.0016 9.77379 18.9662 9.93181 18.896C10.0898 18.8258 10.2334 18.7221 10.3541 18.591C10.4748 18.4599 10.5703 18.304 10.635 18.1324C10.6996 17.9607 10.7322 17.7769 10.7307 17.5914C10.7292 17.406 10.6937 17.2228 10.6263 17.0524C10.5589 16.882 10.461 16.728 10.3382 16.5991L7.38857 13.3957H19.7142C20.0552 13.3957 20.3823 13.2486 20.6234 12.9867C20.8645 12.7248 21 12.3697 21 11.9993C21 11.6289 20.8645 11.2738 20.6234 11.0119C20.3823 10.75 20.0552 10.6029 19.7142 10.6029H7.38857L10.3369 7.40086C10.4597 7.27205 10.5577 7.11796 10.625 6.94759C10.6924 6.77722 10.7279 6.59398 10.7294 6.40856C10.7309 6.22314 10.6983 6.03927 10.6337 5.86765C10.569 5.69603 10.4736 5.54012 10.3528 5.409C10.2321 5.27789 10.0885 5.1742 9.93053 5.10399C9.77251 5.03377 9.6032 4.99844 9.43247 5.00005C9.26174 5.00166 9.09302 5.04019 8.93615 5.11337C8.77927 5.18656 8.63739 5.29294 8.51878 5.42631L3.3756 11.012C3.25648 11.142 3.1621 11.2962 3.09787 11.4659Z"
            fill="white"
          />
        </svg>
      </div>
      <div
        v-if="showLogo"
      >
        <ProjectLogo
          v-if="!Store.sceneType"
          :image="Store.organization_thumbnail"
        />
        <ProjectLogo
          v-if="Store.sceneType"
          :image="
            Store.logo === 'light' ? Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo : Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo_dark"
        />
      </div>
    </div>

    <div class="flex justify-center items-center gap-6 px-8 py-2 absolute top-2 right-0 z-[5]">
      <div
        class="gap-4 flex flex-row justify-end items-center"
      >
        <ControlButton
          v-if="isAndriod && !isOrganization && (Store.projectCardData[route.params.projectId] && !Store.projectCardData[route.params.projectId].projectSettings?.ale?.welcome_video && !Store.projectCardData[route.params.projectId].projectSettings?.ale?.welcome_thumbnail || Store.splashLoader) "
          id="touchscreen"
          :is-hover="showTooltip.includes('touchscreen')"
          :active="Store.isTouchScreen"
          :inactive-s-v-g="toolBtnData.touchInactiveSVG"
          :active-s-v-g="toolBtnData.touchActiveSVG"
          class="!m-0"
          :class="isMobile ? 'h-10 w-10 p-' : ''"
          tooltiptext="Toggle Touchscreen"
          @on-click="onClickButton('touchscreen')"
          @mouseout="removeItem('touchscreen')"
        />

        <ControlButton
          v-if="isAndriod"
          id="fullscreen"
          :is-hover="showTooltip.includes('fullscreen')"
          :active="Store.isFullScreen"
          :inactive-s-v-g="toolBtnData.fullscreenInactiveSVG"
          :active-s-v-g="toolBtnData.fullscreenActiveSVG"
          class="!m-0"
          tooltiptext="Toggle Fullscreen"
          @on-click="onClickButton('fullscreen')"
          @mouseout="removeItem('fullscreen')"
        />
        <ControlButton
          v-if="route.fullPath.includes('/masterscene') ? Store.organizationDetails?.share_masterscene : Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.share_scenes"
          id="share"
          :is-hover="showTooltip.includes('share')"
          :inactive-s-v-g="toolBtnData.sharebuttonSVG"
          :active-s-v-g="toolBtnData.sharebuttonSVG"
          :active="showShareModal"
          class="!m-0"
          tooltiptext="Share"
          @on-click="onClickButton('share')"
          @mouseout="removeItem('share')"
        />
        <ControlButton
          v-if="route.fullPath.includes('masterscene') ? Store.organizationDetails.mastersvg_visibility : Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.svg_visibility"
          id="svgvisibility"
          :is-hover="showTooltip.includes('svgvisibility')"
          :active="Store.svgVisibility.toggle"
          :inactive-s-v-g="toolBtnData.svgVisibilityInActiveSVG"
          :active-s-v-g="toolBtnData.svgVisibilityActiveSVG"
          class="!m-0"
          tooltiptext="Hide all"
          @on-click="onClickButton('svgvisibility')"
          @mouseout="removeItem('svgvisibility')"
        />
        <ControlButton
          v-if="hasNightModeBackground"
          id="daynight_mode"
          :is-hover="showTooltip.includes('daynight_mode')"
          :active="route.query.daynight_mode === 'night'"
          :inactive-s-v-g="toolBtnData.nightbuttonSVG"
          :active-s-v-g="toolBtnData.daybuttonSVG"
          class="!m-0 cursor-pointer"
          :tooltiptext="route.query.daynight_mode === 'night'?'Day Mode':'Night Mode'"
          @on-click="onClickButton('daynight_mode')"
          @mouseout="removeItem('daynight_mode')"
        />

        <DropDownLanguage v-if="Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.supported_languages.length>0" />
        <CurrencyDropDown v-if="route.fullPath.includes('masterscene') ? Store.organizationDetails.exchangeRatio : Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.currency_support" />
        <GoToFavoriteBtn
          v-if="!isOrganization && hasAvailableUnits && (Store.projectCardData[route.params.projectId] && !Store.projectCardData[route.params.projectId].projectSettings?.ale?.welcome_video && !Store.projectCardData[route.params.projectId].projectSettings?.ale?.welcome_thumbnail || Store.splashLoader) && route.params.organizationId !== 'EyyX0K'"
          id="goToFavorite"
          :class="['border-white', openFav ? '!bg-primary !text-primaryText' : '!bg-secondary !text-secondaryText']"
          :svg="toolBtnData.favbuttonSVG"
          text="Favorites"
          :active="openFav"
          :count="route.query.unitplan_id ?
            (Object.keys(Store.unitPlanFavoritesData).length > 0 ?
              Object.keys(Store.unitPlanFavoritesData).length : 0)
            :
            (Object.keys(Store.favoritesData).length > 0 ?
              Object.keys(Store.favoritesData).length : 0)"
          @go-to-favourites="gotoFavourites"
        />
      </div>
    </div>

    <FavoriteView
      v-if="openFav"
      :property="Store.favoritesData"
      @handle-close="() => openFav = false"
      @handle-click="(unit_id)=>{openFav=false;showUnitplanCard=unit_id;gotoUnit();}"
    />

    <UnitPlanFavoriteView
      v-if="isUntiPlanFavoritesOpen"
      :property="Store.unitPlanFavoritesData"
      @handle-close="() => isUntiPlanFavoritesOpen = false"
      @handle-click="(id) => { router.replace({ query: { unitplan_id: id }}); isUntiPlanFavoritesOpen = false; }"
    />
  </div>

  <!-- Mobile View -->
  <div
    v-if="Store.isMobile || Store.isLandscape"
    ref="navbarRefMobile"
    class="navbar flex w-full  justify-between items-center flex-shrink-0 absolute top-0 z-[5] !bg-secondary backdrop-filter "
    :class="Store.isMobile?'h-[60px]':'h-[45px]'"
  >
    <div class="flex z-[5] justify-center items-center gap-6 ">
      <div
        v-if="!isRootcheck"
        class="w-[3.75rem] h-[3.75rem] rounded-lg p-[1.125rem] flex justify-center items-center cursor-pointer"
        @click="movetoprevscene()"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M3.09787 11.4659C3.00053 11.7211 2.97508 12.002 3.02475 12.2729C3.07441 12.5438 3.19696 12.7927 3.37689 12.988L8.52007 18.5737C8.63868 18.7071 8.78056 18.8134 8.93743 18.8866C9.0943 18.9598 9.26303 18.9983 9.43375 18.9999C9.60448 19.0016 9.77379 18.9662 9.93181 18.896C10.0898 18.8258 10.2334 18.7221 10.3541 18.591C10.4748 18.4599 10.5703 18.304 10.635 18.1324C10.6996 17.9607 10.7322 17.7769 10.7307 17.5914C10.7292 17.406 10.6937 17.2228 10.6263 17.0524C10.5589 16.882 10.461 16.728 10.3382 16.5991L7.38857 13.3957H19.7142C20.0552 13.3957 20.3823 13.2486 20.6234 12.9867C20.8645 12.7248 21 12.3697 21 11.9993C21 11.6289 20.8645 11.2738 20.6234 11.0119C20.3823 10.75 20.0552 10.6029 19.7142 10.6029H7.38857L10.3369 7.40086C10.4597 7.27205 10.5577 7.11796 10.625 6.94759C10.6924 6.77722 10.7279 6.59398 10.7294 6.40856C10.7309 6.22314 10.6983 6.03927 10.6337 5.86765C10.569 5.69603 10.4736 5.54012 10.3528 5.409C10.2321 5.27789 10.0885 5.1742 9.93053 5.10399C9.77251 5.03377 9.6032 4.99844 9.43247 5.00005C9.26174 5.00166 9.09302 5.04019 8.93615 5.11337C8.77927 5.18656 8.63739 5.29294 8.51878 5.42631L3.3756 11.012C3.25648 11.142 3.1621 11.2962 3.09787 11.4659Z"
            fill="white"
          />
        </svg>
      </div>
      <div v-else>
        <ProjectLogo
          v-if="isOrganization"
          :image="Store.organization_thumbnail"
        />
        <ProjectLogo
          v-if="!isOrganization"
          :image="Store.logo === 'light' ? Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo : Store.projectCardData?.[route.params.projectId]?.projectSettings?.general?.branding_logo_dark"
        />
      </div>
    </div>

    <div
      class="gap-2 flex flex-row justify-end items-end z-[5] "
    >
      <DropDownLanguage v-if="Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.supported_languages.length>0" />
      <CurrencyDropDown v-if="route.fullPath.includes('masterscene') ? Store.organizationDetails.exchangeRatio : Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.currency_support" />
      <ControlButton
        v-if="!(route.fullPath.includes('masterscene') ? Store.organizationDetails.mastersvg_visibility : Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.svg_visibility)"
        id="svgvisibility"
        :is-hover="showTooltip.includes('svgvisibility')"
        :active="Store.svgVisibility.toggle"
        :inactive-s-v-g="toolBtnData.svgVisibilityInActiveSVG"
        :active-s-v-g="toolBtnData.svgVisibilityActiveSVG"
        class="!m-0"
        tooltiptext="Toggle SVG Visiblity"
        :class="isMobile ? 'h-10 w-10 p-2' : ''"
        @on-click="onClickButton('svgvisibility')"
        @mouseout="removeItem('svgvisibility')"
      />
      <ControlButton
        v-if="hasNightModeBackground"
        id="daynight_mode"
        :is-hover="showTooltip.includes('daynight_mode')"
        :active="route.query.daynight_mode === 'night'"
        :inactive-s-v-g="toolBtnData.nightbuttonSVG"
        :active-s-v-g="toolBtnData.daybuttonSVG"
        class="!m-0"
        tooltiptext="Switch Day/Night Mode"
        :class="isMobile ? 'h-10 w-10 p-2' : ''"
        @on-click="onClickButton('daynight_mode')"
        @mouseout="removeItem('daynight_mode')"
      />
      <GoToFavoriteBtn
        v-if="!isOrganization && hasAvailableUnits"
        id="goToFavorite"
        class="border-white"
        :svg="toolBtnData.favbuttonMobSVG"
        :count="route.query.unitplan_id ?
          (Object.keys(Store.unitPlanFavoritesData).length > 0 ?
            Object.keys(Store.unitPlanFavoritesData).length : 0)
          :
          (Object.keys(Store.favoritesData).length > 0 ?
            Object.keys(Store.favoritesData).length : 0)"

        @go-to-favourites="gotoFavourites"
      />
      <ControlButton
        v-if="route.fullPath.includes('/masterscene') ? Store.organizationDetails?.share_masterscene : Store.projectCardData[route.params.projectId]?.projectSettings?.ale?.share_scenes"
        id="share"
        :is-hover="showTooltip.includes('share')"
        :inactive-s-v-g="toolBtnData.sharebuttonSVG"
        :active-s-v-g="toolBtnData.sharebuttonSVG"
        :active="showShareModal"
        class="!m-0"
        @on-click="onClickButton('share')"
        @mouseout="removeItem('share')"
      />
    </div>
    <FavoriteView
      v-if="openFav"
      :hideStatus="Store.hideStatus"
      :property="Store.favoritesData"
      @handle-close="() => openFav = false"
      @handle-click="(unit_id)=>{openFav=false;showUnitplanCard=unit_id;gotoUnit();}"
    />
    <UnitPlanFavoriteView
      v-if="isUntiPlanFavoritesOpen"
      :property="Store.unitPlanFavoritesData"
      @handle-close="() => isUntiPlanFavoritesOpen = false"
      @handle-click="(id) => { router.replace({ query: { unitplan_id: id }}); isUntiPlanFavoritesOpen = false; }"
    />
  </div>
  <div
    v-if="Store.isMobile || Store.isLandscape"
    class="absolute flex flex-col top-20  z-[1] gap-6"
    :class="[route.query['unit_id'] ? (!Store.unitplanData[route.params.unitplanId]?.unit_type || Store.unitplanData[route.params.unitplanId]?.unit_type === 'flat'?(route.query['type']==='interior' ?'top-44 c1':' top-32 c2'):(route.query['type']==='interior' || route.query['type']==='unitplan' ?'top-44 c3':'top-32 c4')): (route.query['unitplan_id'] ? 'top-32 c5' : 'top-20 c6'),
             Store.isLandscape && route.fullPath.includes('masterscene') ?'!top-14 right-[0.8rem]': !Store.isMobile? '!top-14 right-[5.8rem]' : '!top-40 right-[0.2rem]' , !Store.isLandscape ? '':'right-5']"
  >
    <ControlButton
      v-if="isOrganization && isAndriod"
      id="fullscreen"
      :is-hover="showTooltip.includes('fullscreen')"
      :active="Store.isFullScreen"
      :inactive-s-v-g="toolBtnData.fullscreenInactiveSVG"
      :active-s-v-g="toolBtnData.fullscreenActiveSVG"
      class="!m-0"
      tooltiptext="Toggle fullscreen"
      @on-click="onClickButton(&quot;fullscreen&quot;)"
      @mouseout="removeItem('fullscreen')"
    />

    <ControlButton
      v-if="Store.showRadius && Store.splashLoader"
      id="radius"
      :is-hover="showTooltip.includes('radius')"
      :active="route.query['radius'] === 'true'"
      :inactive-s-v-g="toolBtnData.radiusInactiveSVG"
      :active-s-v-g="toolBtnData.radiusActiveSVG"
      class="!m-0"
      :class="isMobile ? 'h-10 w-10 p-' : ''"
      tooltiptext="radius"
      @on-click="onClickButton(&quot;radius&quot;)"
      @mouseout="removeItem('radius')"
    />

    <!-- <ControlButton
      v-if="Store.showFilter"
      :is-hover="showTooltip.includes('filter')"
      :inactive-s-v-g="toolBtnData.filterInactiveSVG"
      :active-s-v-g="toolBtnData.filterActiveSVG"
      class="!m-0"
      :class="isMobile ? 'h-10 w-10 p-' : ''"
      tooltiptext="Apply Filters"
      @on-click="onClickButton(&quot;filter&quot;)"
      @mouseout="removeItem('filter')"
    /> -->

    <!-- <ControlButton
      v-if="Store.show360 "
      :is-hover="showTooltip.includes('view')"
      :inactive-s-v-g="toolBtnData.viewActiveSVG"
      :active-s-v-g="toolBtnData.viewActiveSVG"
      class="!m-0"
      :class="isMobile ? 'h-10 w-10 p-' : ''"
      tooltiptext="360"
      @on-click="onClickButton(&quot;view&quot;)"
      @mouseout="removeItem('view')"
    /> -->
  </div>
  <ShareModal
    :visible="showShareModal"
    @close="showShareModal = false"
  />
</template>

<style scoped>

.navbar{
padding: var(--25, 10px) var(--4, 16px);
}

svg path{
  fill:var(--secondaryText);
}
</style>
