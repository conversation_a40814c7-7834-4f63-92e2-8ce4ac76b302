<script setup>
import { ref, defineProps, defineEmits, watch, onMounted, computed } from 'vue';
import { creationToolStore } from '../../../store';
import MLEViewer from '../../../views/MLEViewer.vue';
import { useRoute } from 'vue-router';
import { cdn, Googleanalytics } from '../../../helpers/helper';

const route = useRoute();
const Store = creationToolStore();
const props = defineProps({
  showInterior: {type: String, default: ""},
  currentImageId: {type: String, default: ""},
});

const imageId = props.currentImageId ? props.currentImageId : '';
const updatedCurrentImageId = ref(props.currentImageId);
const emit = defineEmits(['updatedImageId', 'currentImageIdEmit']);
const storagePath = computed(() => {
  return `https://${import.meta.env.VITE_APP_BUCKET_CDN}/CreationtoolAssets/${route.params.organizationId}/projects/${route.params.projectId}/tours/${props.showInterior}`;
});
const vrData = computed(() => Store.listOfVrTourData[props.showInterior] || {});

// Computed property for filtered images with updated_at
const filteredImages = computed(() => {
  if (!vrData.value || !vrData.value.images) {
    return {};
  }

  const result = {};

  Object.keys(vrData.value.images).forEach((key) => {
    const imageData = vrData.value.images[key];
    if (imageData && imageData.updated_at && imageData.name) {
      // Remove file extension from the name
      const nameWithoutExtension = imageData.name.replace(/\.[^/.]+$/, "");
      result[nameWithoutExtension] = {
        updated_at: imageData.updated_at,
      };
    }
  });

  return result;
});

const tourType = computed(() => vrData.value.type || vrData.value.virtualtour_type);

const isMatterport = computed(() => tourType.value === 'matterport');
const isExternal = computed(() => tourType.value === 'external');
const isMLE = computed(() => tourType.value === 'MLE');

const shouldShowIframe = computed(() => isMatterport.value || isExternal.value);

const iframeSrc = computed(() => {
  if (isMatterport.value) {
    return `https://showcase.propvr.tech/?m=${vrData.value.space_id}&play=1/${imageId}`;
  }
  if (isExternal.value) {
    return `${vrData.value.link}/${imageId}`;
  }
  return '';
});
watch(() => props.currentImageId, (newVal) => {
  updatedCurrentImageId.value = newVal;
  if (!isMLE.value){
    const iframe = document.getElementById('showcase_frame');
    const action = {
      simulate: false,
      actiontype: "fp_image_change",
      data: updatedCurrentImageId.value,
    };
    iframe.contentWindow.postMessage(JSON.stringify(action), '*');
    emit("currentImageIdEmit", updatedCurrentImageId.value);
  }
});

function handlePostMessage (event) {
  const trustedOrigins = "https://fp.propvr.tech";
  if (!trustedOrigins.includes(event.origin)) {
    return;
  }

  const parsedData = JSON.parse(event.data);

  updatedCurrentImageId.value = event.data;
  emit('updatedImageId', event.data);
  emit("currentImageIdEmit", event.data);

  if (parsedData.name) {
    Googleanalytics("vr_tour_image_click", {
      organization_id: route.params.organizationId,
      organization_name: Store.organization_name,
      project_name: Store.projectCardData?.[route.params.projectId]?.name,
      project_id: route.params.projectId,
      image_name: parsedData.name,
      image_id: parsedData.id,
      tour_id: vrData.value._id,
      tour_name: vrData.value.tour_name,
    });
  }

}

onMounted(() => {
  window.addEventListener("message", handlePostMessage);
});
</script>

<template>
  <iframe
    v-if="shouldShowIframe"
    id="showcase_frame"
    class="w-full h-[100vh]"
    :src="iframeSrc"
    frameborder="0"
    allowfullscreen
    allow="xr-spatial-tracking"
  />

  <MLEViewer
    v-else-if="isMLE"
    :key="props.showInterior"
    :modelURL="cdn(vrData.model)"
    :cameraURL="cdn(vrData.camera)"
    :storagePath="storagePath"
    :initialRotation="vrData.initial_rotation"
    :tourLabels="vrData.labels"
    :isMobile="Store.isMobile"
    :activeLabel="updatedCurrentImageId"
    :filteredImages="filteredImages"
    @current-image-id-emit="(currentId)=>{emit('currentImageIdEmit', currentId);}"
  />
</template>
