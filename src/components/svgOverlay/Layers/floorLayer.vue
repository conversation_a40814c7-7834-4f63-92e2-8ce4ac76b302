<script setup>
import {defineProps, ref, watch, onMounted, computed} from 'vue';
import { creationToolStore } from '../../../store';
import { useRoute } from 'vue-router';
import router from '../../../router';
import { onClickOutside } from '@vueuse/core';
import FloorCard from '../../ALEComponents/FloorCardComp/FloorCardComp.vue';
import {cardPosition} from '../../../helpers/cardPosition';
// Import SideBar from '../../ALEComponents/SideBar/SideDrawer.vue';
// Import SearchComponent from '../../ALEComponents/SearchComponent/SearchComponent.vue';
// Import SearchComponentMob from '../../ALEComponents/SearchComponentMob/SearchComponentMob.vue';
import FloorSwitchSlider from '../../ALEComponents/FloorSwitchSlider/FloorSwitchSlider.vue';
// Import FilterBar from '../../ALEComponents/FilterBar/FilterBar.vue';
// Import FilterButton from '../../ALEComponents/FilterButton/FilterButtonComponent.vue';
// Import ControlButton from '../../ALEComponents/ControlButton/ControlButton.vue';
// Import Toast from '../../Overall/Toast/CustomToast.vue';
// Import { isRealValue } from '../../../helpers/helper';
import { addSVGDeepZoom, Googleanalytics, setActiveElem, cdn } from '../../../helpers/helper';
import OpenSeadragon from 'openseadragon';
import { fetchVideo } from '../../../helpers/API';
import TranslationComp from '../../ALEComponents/TranslationComp/TranslationComp.vue';
import { availUnitOrgs } from '../../../config/masterdata';
// Var showTooltip = ref([]);
// Const layersId = ref(new Set());
const route = useRoute();
const Store = creationToolStore();
const floorSwitchEnableIds = ref(null);
const startTime = ref(new Date), timespent = ref(0);
// Const isShowModal = ref(false);
// Const openFilterModal = ref(false);
// Const filterBtnToggle = ref(false);
// Const filterBar = ref();
// Const filterBarBtn = ref();
// Const searchResultToast = ref(false);
// Const toolBtnData = ref({
//   FilterActiveSVG: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
// <path d="M5.9454 11.1866V2.49996C5.9454 2.27895 5.85997 2.06698 5.7079 1.9107C5.55584 1.75442 5.3496 1.66663 5.13455 1.66663C4.9195 1.66663 4.71325 1.75442 4.56119 1.9107C4.40913 2.06698 4.3237 2.27895 4.3237 2.49996V11.1866C3.7944 11.3597 3.3324 11.7014 3.00456 12.1622C2.67671 12.623 2.5 13.1791 2.5 13.75C2.5 14.3208 2.67671 14.8769 3.00456 15.3377C3.3324 15.7985 3.7944 16.1402 4.3237 16.3133V17.5C4.3237 17.721 4.40913 17.9329 4.56119 18.0892C4.71325 18.2455 4.9195 18.3333 5.13455 18.3333C5.3496 18.3333 5.55584 18.2455 5.7079 18.0892C5.85997 17.9329 5.9454 17.721 5.9454 17.5V16.3133C6.4747 16.1402 6.93669 15.7985 7.26454 15.3377C7.59239 14.8769 7.76909 14.3208 7.76909 13.75C7.76909 13.1791 7.59239 12.623 7.26454 12.1622C6.93669 11.7014 6.4747 11.3597 5.9454 11.1866ZM5.13455 14.7916C4.93408 14.7916 4.73812 14.7305 4.57144 14.6161C4.40476 14.5016 4.27485 14.3389 4.19814 14.1486C4.12142 13.9582 4.10135 13.7488 4.14046 13.5467C4.17957 13.3447 4.2761 13.1591 4.41785 13.0134C4.5596 12.8677 4.7402 12.7685 4.93681 12.7283C5.13342 12.6881 5.33722 12.7087 5.52242 12.7876C5.70762 12.8664 5.86592 12.9999 5.97729 13.1712C6.08866 13.3425 6.14811 13.5439 6.14811 13.75C6.14768 14.0261 6.04076 14.2908 5.85077 14.486C5.66078 14.6813 5.40323 14.7912 5.13455 14.7916Z" fill="white"/>
// <path d="M17.5 13.75C17.4981 13.1795 17.3205 12.6243 16.9928 12.1638C16.6651 11.7034 16.204 11.3613 15.6756 11.1866V2.49996C15.6756 2.27895 15.5902 2.06698 15.4381 1.9107C15.286 1.75442 15.0798 1.66663 14.8647 1.66663C14.6497 1.66663 14.4434 1.75442 14.2914 1.9107C14.1393 2.06698 14.0539 2.27895 14.0539 2.49996V11.1866C13.5246 11.3597 13.0626 11.7014 12.7347 12.1622C12.4069 12.623 12.2302 13.1791 12.2302 13.75C12.2302 14.3208 12.4069 14.8769 12.7347 15.3377C13.0626 15.7985 13.5246 16.1402 14.0539 16.3133V17.5C14.0539 17.721 14.1393 17.9329 14.2914 18.0892C14.4434 18.2455 14.6497 18.3333 14.8647 18.3333C15.0798 18.3333 15.286 18.2455 15.4381 18.0892C15.5902 17.9329 15.6756 17.721 15.6756 17.5V16.3133C16.204 16.1386 16.6651 15.7966 16.9928 15.3361C17.3205 14.8757 17.4981 14.3204 17.5 13.75ZM14.8647 14.7916C14.6643 14.7916 14.4683 14.7305 14.3016 14.6161C14.135 14.5016 14.005 14.3389 13.9283 14.1486C13.8516 13.9582 13.8315 13.7488 13.8707 13.5467C13.9098 13.3447 14.0063 13.1591 14.148 13.0134C14.2898 12.8677 14.4704 12.7685 14.667 12.7283C14.8636 12.6881 15.0674 12.7087 15.2526 12.7876C15.4378 12.8664 15.5961 12.9999 15.7075 13.1712C15.8189 13.3425 15.8783 13.5439 15.8783 13.75C15.8779 14.0261 15.7709 14.2908 15.581 14.486C15.391 14.6813 15.1334 14.7912 14.8647 14.7916Z" fill="white"/>
// <path d="M12.6349 6.24996C12.633 5.67948 12.4554 5.12426 12.1277 4.66381C11.8 4.20336 11.3389 3.8613 10.8105 3.68663V2.49996C10.8105 2.27895 10.7251 2.06698 10.573 1.9107C10.4209 1.75442 10.2147 1.66663 9.99964 1.66663C9.78459 1.66663 9.57835 1.75442 9.42629 1.9107C9.27422 2.06698 9.18879 2.27895 9.18879 2.49996V3.68663C8.65949 3.85974 8.1975 4.20141 7.86965 4.66223C7.5418 5.12304 7.3651 5.6791 7.3651 6.24996C7.3651 6.82082 7.5418 7.37687 7.86965 7.83769C8.1975 8.2985 8.65949 8.64018 9.18879 8.81329V17.5C9.18879 17.721 9.27422 17.9329 9.42629 18.0892C9.57835 18.2455 9.78459 18.3333 9.99964 18.3333C10.2147 18.3333 10.4209 18.2455 10.573 18.0892C10.7251 17.9329 10.8105 17.721 10.8105 17.5V8.81329C11.3389 8.63862 11.8 8.29656 12.1277 7.83611C12.4554 7.37566 12.633 6.82044 12.6349 6.24996ZM9.99964 7.29163C9.79918 7.29163 9.60322 7.23053 9.43654 7.11607C9.26986 7.00161 9.13995 6.83893 9.06323 6.64859C8.98652 6.45825 8.96645 6.2488 9.00556 6.04674C9.04466 5.84468 9.1412 5.65907 9.28295 5.51339C9.4247 5.36771 9.60529 5.2685 9.80191 5.22831C9.99852 5.18812 10.2023 5.20874 10.3875 5.28758C10.5727 5.36643 10.731 5.49994 10.8424 5.67124C10.9538 5.84254 11.0132 6.04394 11.0132 6.24996C11.0128 6.52609 10.9059 6.79079 10.7159 6.98604C10.5259 7.1813 10.2683 7.29119 9.99964 7.29163Z" fill="white"/>
// </svg>`,
//   FilterInactiveSVG: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
// <path d="M5.9454 11.1866V2.49996C5.9454 2.27895 5.85997 2.06698 5.7079 1.9107C5.55584 1.75442 5.3496 1.66663 5.13455 1.66663C4.9195 1.66663 4.71325 1.75442 4.56119 1.9107C4.40913 2.06698 4.3237 2.27895 4.3237 2.49996V11.1866C3.7944 11.3597 3.3324 11.7014 3.00456 12.1622C2.67671 12.623 2.5 13.1791 2.5 13.75C2.5 14.3208 2.67671 14.8769 3.00456 15.3377C3.3324 15.7985 3.7944 16.1402 4.3237 16.3133V17.5C4.3237 17.721 4.40913 17.9329 4.56119 18.0892C4.71325 18.2455 4.9195 18.3333 5.13455 18.3333C5.3496 18.3333 5.55584 18.2455 5.7079 18.0892C5.85997 17.9329 5.9454 17.721 5.9454 17.5V16.3133C6.4747 16.1402 6.93669 15.7985 7.26454 15.3377C7.59239 14.8769 7.76909 14.3208 7.76909 13.75C7.76909 13.1791 7.59239 12.623 7.26454 12.1622C6.93669 11.7014 6.4747 11.3597 5.9454 11.1866ZM5.13455 14.7916C4.93408 14.7916 4.73812 14.7305 4.57144 14.6161C4.40476 14.5016 4.27485 14.3389 4.19814 14.1486C4.12142 13.9582 4.10135 13.7488 4.14046 13.5467C4.17957 13.3447 4.2761 13.1591 4.41785 13.0134C4.5596 12.8677 4.7402 12.7685 4.93681 12.7283C5.13342 12.6881 5.33722 12.7087 5.52242 12.7876C5.70762 12.8664 5.86592 12.9999 5.97729 13.1712C6.08866 13.3425 6.14811 13.5439 6.14811 13.75C6.14768 14.0261 6.04076 14.2908 5.85077 14.486C5.66078 14.6813 5.40323 14.7912 5.13455 14.7916Z" fill="white"/>
// <path d="M17.5 13.75C17.4981 13.1795 17.3205 12.6243 16.9928 12.1638C16.6651 11.7034 16.204 11.3613 15.6756 11.1866V2.49996C15.6756 2.27895 15.5902 2.06698 15.4381 1.9107C15.286 1.75442 15.0798 1.66663 14.8647 1.66663C14.6497 1.66663 14.4434 1.75442 14.2914 1.9107C14.1393 2.06698 14.0539 2.27895 14.0539 2.49996V11.1866C13.5246 11.3597 13.0626 11.7014 12.7347 12.1622C12.4069 12.623 12.2302 13.1791 12.2302 13.75C12.2302 14.3208 12.4069 14.8769 12.7347 15.3377C13.0626 15.7985 13.5246 16.1402 14.0539 16.3133V17.5C14.0539 17.721 14.1393 17.9329 14.2914 18.0892C14.4434 18.2455 14.6497 18.3333 14.8647 18.3333C15.0798 18.3333 15.286 18.2455 15.4381 18.0892C15.5902 17.9329 15.6756 17.721 15.6756 17.5V16.3133C16.204 16.1386 16.6651 15.7966 16.9928 15.3361C17.3205 14.8757 17.4981 14.3204 17.5 13.75ZM14.8647 14.7916C14.6643 14.7916 14.4683 14.7305 14.3016 14.6161C14.135 14.5016 14.005 14.3389 13.9283 14.1486C13.8516 13.9582 13.8315 13.7488 13.8707 13.5467C13.9098 13.3447 14.0063 13.1591 14.148 13.0134C14.2898 12.8677 14.4704 12.7685 14.667 12.7283C14.8636 12.6881 15.0674 12.7087 15.2526 12.7876C15.4378 12.8664 15.5961 12.9999 15.7075 13.1712C15.8189 13.3425 15.8783 13.5439 15.8783 13.75C15.8779 14.0261 15.7709 14.2908 15.581 14.486C15.391 14.6813 15.1334 14.7912 14.8647 14.7916Z" fill="white"/>
// <path d="M12.6349 6.24996C12.633 5.67948 12.4554 5.12426 12.1277 4.66381C11.8 4.20336 11.3389 3.8613 10.8105 3.68663V2.49996C10.8105 2.27895 10.7251 2.06698 10.573 1.9107C10.4209 1.75442 10.2147 1.66663 9.99964 1.66663C9.78459 1.66663 9.57835 1.75442 9.42629 1.9107C9.27422 2.06698 9.18879 2.27895 9.18879 2.49996V3.68663C8.65949 3.85974 8.1975 4.20141 7.86965 4.66223C7.5418 5.12304 7.3651 5.6791 7.3651 6.24996C7.3651 6.82082 7.5418 7.37687 7.86965 7.83769C8.1975 8.2985 8.65949 8.64018 9.18879 8.81329V17.5C9.18879 17.721 9.27422 17.9329 9.42629 18.0892C9.57835 18.2455 9.78459 18.3333 9.99964 18.3333C10.2147 18.3333 10.4209 18.2455 10.573 18.0892C10.7251 17.9329 10.8105 17.721 10.8105 17.5V8.81329C11.3389 8.63862 11.8 8.29656 12.1277 7.83611C12.4554 7.37566 12.633 6.82044 12.6349 6.24996ZM9.99964 7.29163C9.79918 7.29163 9.60322 7.23053 9.43654 7.11607C9.26986 7.00161 9.13995 6.83893 9.06323 6.64859C8.98652 6.45825 8.96645 6.2488 9.00556 6.04674C9.04466 5.84468 9.1412 5.65907 9.28295 5.51339C9.4247 5.36771 9.60529 5.2685 9.80191 5.22831C9.99852 5.18812 10.2023 5.20874 10.3875 5.28758C10.5727 5.36643 10.731 5.49994 10.8424 5.67124C10.9538 5.84254 11.0132 6.04394 11.0132 6.24996C11.0128 6.52609 10.9059 6.79079 10.7159 6.98604C10.5259 7.1813 10.2683 7.29119 9.99964 7.29163Z" fill="white"/>
// </svg>`,
// });
const { layerData, sceneType } = defineProps({
  layerData: {
    type: Object,
    default () {
      return {};
    },
  },
  sceneType: {
    type: String,
    default: '',
  },
});
const clickedId=ref(false), showunitCard=ref(false), position=ref(false), sceneId=ref(false), floorId=ref(false), buildingId=ref(false), floorsMobileViewToggle = ref(false), floorSVGElem = ref({}), list = ref(), layerId = ref();
fetchVideo(layerData, cdn(Store.SceneData[route.params.sceneId].sceneData.background?.low_resolution));
// Computed property to sort floors by 'order'
const sortedFloors = computed(() => {
  return Object.values(Store.allBuildings[String(Object.values(layerData)[0].layer_data.building_id)].floors)
    .sort((a, b) => a.order - b.order);
});
if (Object.keys(layerData).length > 0) {
  floorSwitchEnableIds.value = Object.values(layerData).map( (layer) => {
    if (Store.getFloorStatus(layer.layer_data.floor_id, layer.layer_data.building_id)) {
      return String(layer.layer_data.floor_id);
    }
    return undefined;
  }).filter((floor) => String(floor));
}
// Const onClickButton = (id) => {
//   If (id === 'filter') {
//     OpenFilterModal.value = true;
//   }
// };
watch(() => [Store.buildingData, Store.unitData], () => {
  if (Object.keys(layerData).length > 0) {
    floorSwitchEnableIds.value = Object.values(layerData).map( (layer) => {
      if (Store.getFloorStatus(layer.layer_data.floor_id, layer.layer_data.building_id)) {
        return String(layer.layer_data.floor_id);
      }
      return undefined;
    }).filter((floor) => String(floor));
  }
});

// Const floorToLayerMap = Object.keys(layerData).reduce((map, key) => {
//   Const { floor_id, layer_id, building_id } = layerData[key].layer_data;
//   Map[floor_id] = { layerId: layer_id, buildingId: building_id };
//   Return map;
// }, {});

// Watch(() => Store.filteredUnits, () => {
//   If (!isRealValue(Store.filteredUnits)) {
//     Return;
//   }

//   Const resultArray = [];
//   Const floorSet = new Set();
//   Const layersSet = new Set();

//   For (const filteredUnit of Object.values(Store.filteredUnits)) {
//     Const unit_id = filteredUnit._id;
//     Const floor_id = Store.unitData[unit_id]?.floor_id;

//     If (floor_id) {
//       Const layer_id = floorToLayerMap[floor_id];
//       If (layer_id !== undefined) {
//         FloorSet.add(floor_id);
//         LayersSet.add(layer_id);
//       }
//     }

//     ResultArray.push(unit_id);
//   }

//   If (layersSet.size !== 0) {
//     LayersId.value = layersSet;
//   }
// });

watch(() => Store.currentZoomlevel, () => {
  setActiveElem(floorSVGElem.value, Store.currentZoomlevel);
});

function moveToNxtScene (building_id, id, floor_id, scene_id, event) {
  layerId.value=id;
  const svgGroup = document.getElementById(id);
  const rect = svgGroup.getBoundingClientRect();
  const cursorX = event.clientX;
  const cursorY = event.clientY;
  const cardWidth = 300;
  const cardHeight = 160;
  const newPosition = cardPosition(rect, cursorX, cursorY, cardWidth, cardHeight);
  position.value=newPosition;
  clickedId.value = id;
  sceneId.value = scene_id;
  floorId.value = floor_id;
  buildingId.value = building_id;
  showunitCard.value = true;
}

function closeModal (){
  showunitCard.value=false;
  clickedId.value=false;
}
function moveToScene () {

  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  if (layerData[layerId.value].layer_data.video_tag){
    Store.currentSceneVideo=layerData[layerId.value].layer_data.video_tag;
    Store.currentSceneVideoThumb=Store.SceneData[route.params.sceneId].sceneData.background.low_resolution;
    window.asd = function (){
      router.push({ name: "projectScene", params: { sceneId: sceneId.value }, query: { ...route.query, building_id: buildingId.value, floor_id: floorId.value, status: availUnitOrgs.includes(route.params.organizationId)?'available':""}});
    };
  } else {
    router.push({ name: "projectScene", params: { sceneId: sceneId.value }, query: { ...route.query, building_id: buildingId.value, floor_id: floorId.value, status: availUnitOrgs.includes(route.params.organizationId)?'available':""}});
  }
  timespent.value = difference;
  startTime.value=new Date();

  const formatAreaString = (data) => {
    const unit = data.measurementType || "sqft"; // Default to "sqft" if empty
    return `${data.minArea} ${unit} - ${data.maxArea} ${unit}`;
  };

  Googleanalytics("floor_clicked", {
    floor_id: floorId.value,
    floor_name: floorId.value,
    building_id: buildingId.value,
    building_name: Store.buildingData[buildingId.value].name,
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
    number_units: Store.floorData[buildingId.value][floorId.value].totalAvailableUnits,
    project_city: Store.projectCardData[route.params.projectId].city,
    project_type: Store.buildingData[buildingId.value]?.category,
    availability_tag: Store.floorData[buildingId.value][floorId.value].status,
    unit_size: formatAreaString(Store.floorData[buildingId.value][floorId.value]),
    // floor_timespent: timespent.value,
    // floor_timestart: startTime.value,
  });
}
// Function gotoUnit (unit_id, unitplan_id){
//   Router.push({name: "unit.Child", params: {unitplanId: unitplan_id}, query: {...route.query, unit_id: unit_id}});
// }

const handleFloorSwitch = (val, event) => {
  if (floorsMobileViewToggle.value){
    if (floorSwitchEnableIds.value.includes(String(val))){
      Object.values(layerData).forEach((item) => {
        if (String(item.layer_data.floor_id) === String(val)){
          moveToNxtScene(item.layer_data.building_id, item.layer_data.layer_id, item.layer_data.floor_id, item.layer_data.scene_id, event);
        }
      });
    } else {
      clickedId.value=false;
      sceneId.value=false;
      floorId.value=false;
      buildingId.value=false;
      showunitCard.value=false;
    }
  }
};

const bedroomRange = (data) => {
  if (!data || !Array.isArray(data)) {
    return '';
  }

  // Separate numeric and non-numeric unit types
  const nonNumericUnits = data.filter((type) => isNaN(parseInt(type, 10)));
  const numericUnits = data
    .map((type) => parseInt(type, 10))
    .filter(Boolean)
    .sort((a, b) => a - b);

  // Handling numeric units
  let numericRange = '';
  if (numericUnits.length === 1) {
    numericRange = `${numericUnits[0]} BR`;
  } else if (numericUnits.length > 1) {
    numericRange = `${numericUnits.slice(0, -1).join(', ')} & ${numericUnits.at(-1)} BR`;
  }

  // Combining non-numeric and numeric ranges
  if (nonNumericUnits.length === 1 && numericUnits.length === 1) {
    // One numeric and one non-numeric
    return `${nonNumericUnits[0]} & ${numericRange}`;
  }
  const formattedNonNumeric = nonNumericUnits.length ? nonNumericUnits.join(', ') : '';
  if (formattedNonNumeric && numericRange) {
    return `${formattedNonNumeric}, ${numericRange}`;
  } else if (formattedNonNumeric) {
    return formattedNonNumeric;
  }

  return numericRange;
};

// Function showModal () {
//   FilterBtnToggle.value = !filterBtnToggle.value;
//   IsShowModal.value = !isShowModal.value;
// }

// Function closeFilterModal () {
//   FilterBtnToggle.value = false;
//   IsShowModal.value = false;
// }

// Function handleResults () {
//   Const elementValues = Object.values(floorToLayerMap).filter((value) => {
//     Return !layersId.value.has(value);
//   });

//   If (layersId.value.size === 0) {
//     Return;
//   }
//   ElementValues.forEach((elementValue) => {
//     Const elementId = document.getElementById(elementValue.layerId);
//     If (elementId) {
//       ElementId.style.fill = 'black';
//     }
//   });
//   OpenFilterModal.value = false;
// }

// Function handleClear () {
//   Const elementValues = Object.entries(floorToLayerMap).filter(([_, layerInfo]) => {
//     Return !layersId.value.has(layerInfo);
//   });
//   ElementValues.forEach(([floorId, layerInfo]) => {
//     If (Store.getFloorStatus(floorId, layerInfo.buildingId)) {
//       Const elementId = document.getElementById(layerInfo.layerId);
//       If (elementId) {
//         ElementId.style = 'fill-tower-available';
//       }
//     }
//   });
//   OpenFilterModal.value = false;
// }

// Function handleSearchError() {
//   OpenFilterModal.value = false;
//   SearchResultToast.value = true;
// }

// Function closeToast () {
//   SearchResultToast.value = false;
// }

// Function removeItem (id) {
//   Const index = showTooltip.value.findIndex((item) => item === id);

//   If (index !== -1) {
//     ShowTooltip.value.splice(index, 1);
//   }
// }

// OnClickOutside(filterBar, () => closeFilterModal(), { ignore: [filterBarBtn] });
if (sceneType==='deep_zoom') {
  window.viewer.addHandler('canvas-drag', () => {
    if (clickedId.value) {
      const floorElem = floorSVGElem.value[clickedId.value].g;
      floorElem.classList.remove('!fill-[#ffffff00]');
    }
    if (showunitCard.value){
      showunitCard.value=false;
    }
  });
  window.viewer.addHandler('zoom', function () {
    if (showunitCard.value){
      const floorElem = floorSVGElem.value[clickedId.value].g;
      floorElem.classList.remove('!fill-[#ffffff00]', 'opacity-8', '!opacity-70');
      closeModal();
    }
  });
}
if (sceneType==='deep_zoom'){
  onMounted(() => {
    if (Object.values(layerData).length > 0) {
      Object.values(layerData).forEach(async (item) => {
        const requestOptions = {
          method: "GET",
          redirect: "follow",
        };
        const response = await fetch(cdn(item.layer), requestOptions);
        const svgString = await response.text();
        const obj = addSVGDeepZoom({
          g: svgString,
          zIndex: item.layer_data.zIndex,
          reSize: item.layer_data.reSize,
          x: item.layer_data.x,
          y: item.layer_data.y,
          width: item.layer_data.width,
          height: item.layer_data.height,
          placement: item.layer_data.placement,
          layer_id: item.layer_data.layer_id,
        }, window.viewer);
        obj.svgElement.children[0].classList.add('!cursor-pointer', '!opacity-40');
        floorSVGElem.value[item.layer_data.layer_id] = {'g': obj.svgElement.children[0], 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel};
        if (Store.getFloorStatus(item.layer_data.floor_id, item.layer_data.building_id)){
          obj.svgElement.children[0].classList.add('fill-tower-available', 'stroke-tower-available', 'stroke-[0.2rem]', 'opacity-8');
        } else {
          obj.svgElement.children[0].classList.add('!fill-none', 'hidden');
        }
        if (item.layer_data.minZoomLevel  && item.layer_data.maxZoomLevel){
          if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel<= item.layer_data.maxZoomLevel){
            obj.svgElement.children[0].classList.add('!visible');
            obj.svgElement.children[0].classList.remove('!hidden');
          } else {
            obj.svgElement.children[0].classList.remove('!visible');
            obj.svgElement.children[0].classList.add('!hidden');
          }
        }
        new OpenSeadragon.MouseTracker({
          element: obj.svgElement.children[0],
          clickHandler: function (e) {
            if (Store.isTouchScreen || Store.isMobile){
              if (clickedId.value){
                floorSVGElem.value[clickedId.value].g.classList.remove('!fill-[#ffffff00]');
              }
              obj.svgElement.children[0].classList.add('!fill-[#ffffff00]');
              showunitCard.value=false;
              moveToNxtScene(item.layer_data.building_id, item.layer_data.layer_id, item.layer_data.floor_id, item.layer_data.scene_id, e);
            } else {
              obj.svgElement.children[0].classList.add('!fill-[#ffffff00]');
              moveToScene();
            }
            clickedId.value=item.layer_data.layer_id;
          },
        });
        obj.svgElement.children[0].addEventListener("mouseenter", (e) => {
          if (!Store.isMobile && !Store.isTouchScreen){
            if (clickedId.value!==item.layer_data.layer_id && !clickedId.value){
              obj.svgElement.children[0].classList.add('!fill-[#ffffff00]');
              showunitCard.value=false;
              moveToNxtScene(item.layer_data.building_id, item.layer_data.layer_id, item.layer_data.floor_id, item.layer_data.scene_id, e);
            }
          } else {
            if (clickedId.value!==item.layer_data.layer_id && !clickedId.value){
              obj.svgElement.children[0].classList.add('!fill-[#ffffff00]');
            }
          }
        },
        );
        obj.svgElement.children[0].addEventListener("mouseleave", () => {
          if (Store.isTouchScreen || Store.isMobile){
            if (clickedId.value!==item.layer_data.layer_id && !clickedId.value){
              obj.svgElement.children[0].classList.remove('!fill-[#ffffff00]');
            }
          } else {
            closeModal();
            obj.svgElement.children[0].classList.remove('!fill-[#ffffff00]');
          }
        },
        );
      });
    }
  });
}

watch(() => Store.svgVisibility.showSVG, (isVisible) => {
  if (sceneType !== 'deep_zoom') {
    return;
  }

  Object.values(layerData).forEach((layer) => {
    if (floorSVGElem.value[layer.layer_data.layer_id]) {
      const floorElement = floorSVGElem.value[layer.layer_data.layer_id].g;
      if (isVisible) {
        floorElement.classList.add('fill-tower-available', 'stroke-tower-available', 'stroke-[0.2rem]', 'opacity-8');
      } else {
        floorElement.classList.remove('fill-tower-available', 'stroke-tower-available', 'stroke-[0.2rem]', 'opacity-8');
        floorElement.classList.add('fill-none');
      }
    }
  });
});
onClickOutside(list, (event) => {
  if (!document.getElementById(clickedId.value).contains(event.target)){
    if (sceneType==='deep_zoom'){
      const floorElem = floorSVGElem.value[clickedId.value].g;
      floorElem.classList.remove('!fill-[#ffffff00]', 'opacity-8', '!opacity-70');
    }
    showunitCard.value = false;
    clickedId.value=false;

  }
});
</script>
<template>
  <!-- eslint-disable vue/no-v-html -->
  <template v-if="Store.svgVisibility.showSVG">
    <g
      v-for="(layer,index) in layerData"
      v-show="sceneType!=='deep_zoom'"
      :id="layer.layer_data.layer_id"
      :key="index"
      :class="sceneType!=='deep_zoom'?[
        'cursor-pointer opacity-40 ' ,
        'hover:fill-white hover:opacity-30',
        layer.layer.getAttribute('class') +' '+layer.layer_data.type,
        clickedId==layer.layer_data.layer_id && clickedId!=false?'fill-tower-available':'bg-none',
        Store.getFloorStatus(layer.layer_data.floor_id,layer.layer_data.building_id)?'fill-tower-available':'fill-none'
      ]:''"
      @click="Store.isMobile || Store.isTouchScreen || Store.isLandscape ?moveToNxtScene(layer.layer_data.building_id,layer.layer_data.layer_id,layer.layer_data.floor_id,layer.layer_data.scene_id,$event):moveToScene(layer.layer_data.building_id , layer.layer_data.floor_id)"
      @mouseleave="!Store.isMobile && !Store.isTouchScreen && !Store.isLandscape ?closeModal():false"
      @mouseenter="!Store.isMobile && !Store.isTouchScreen && !Store.isLandscape ?moveToNxtScene(layer.layer_data.building_id,layer.layer_data.layer_id,layer.layer_data.floor_id,layer.layer_data.scene_id,$event):false"
      v-html="layer.layer.innerHTML"
    />
  </template>

  <!--eslint-enable-->

  <portal to="floor">
    <!-- <div
      v-if="Store.isMobile && Store.allUnitCardData"
      class="absolute top-0 left-0 w-screen h-screen pointer-events-none"
    >
      <div class="pointer-events-auto">
        <SearchComponentMob
          :data="Store.allUnitCardData"
          :buildingData="Store.buildingData"
          :hideStatus="Store.hideStatus"
          :favUnits="Store.favoritesData"
          @goto-unit="gotoUnit"
          @toggle-button="Store.updateAddRemovefavorites"
        />
      </div>
    </div> -->
    <div
      v-if="showunitCard && floorId"
      ref="list"
      class=" fixed"
      :class="[Store.isMobile ? 'w-full !bottom-0 z-[9]' : Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0 ? '!bottom-4 !right-24 !z-[9]' : Store.isLandscape ? '!bottom-56 !-right-16 !z-[9]' : 'z-[9] top-0', Store.isTouchScreen ? 'w-full h-fit ' : '']"
    >
      <FloorCard
        :class=" Store.isMobile || Store.isLandscape ? ' slide-in-blurred-bottom ' : 'pop'"
        :style="(!Store.isMobile && !Store.isLandscape)?{ 'top': position.y+ 'px','left': position.x + 'px' }:''"
        :towerName="Store.buildingData[buildingId].name"
        :projectName="Store.projectCardData[route.params.projectId].name"
        :availability-status="Store.floorData[buildingId][floorId].status"
        :available-units="Store.floorData[buildingId][floorId].totalAvailableUnits"
        :floor="Store.floorData[buildingId][floorId].floors"
        :unit="Store.floorData[buildingId][floorId].units"
        :min-area="Store.floorData[buildingId][floorId].minArea"
        :max-area="Store.floorData[buildingId][floorId].maxArea"
        :measurement-type="Store.floorData[buildingId][floorId].measurementType"
        :bedrooms="bedroomRange(Store.floorData[buildingId][floorId].uniqueBedrooms)"
        :isMobile="Store.isMobile"
        :isTouchScreen="sceneType==='deep_zoom'?true:Store.isTouchScreen"
        :hideStatus="Store.hideStatus"
        @close-modal="closeModal"
        @move-to-scene="moveToScene"
      />
    </div>
    <div
      v-if="Store.allBuildings && Store.isLandscape"
      ref="FloorSwitchSliderRef"
      class="fixed right-0  h-fit w-[2.12rem]"
      :class="Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0 ? 'right-28  !-bottom-6 rotate-90': Store.isLandscape? 'right-12 -bottom-6 rotate-90':'top-[24%] md:hidden '"
    >
      <button
        :onclick="() => floorsMobileViewToggle = !floorsMobileViewToggle"
        type="button"
        :class="[(floorsMobileViewToggle ? '' : 'rounded-bl-lg'),'flex flex-col justify-center bg-secondary text-secondaryText gap-5 py-4 items-center bg-opacity-60 rounded-tl-lg backdrop-blur-[80px]' ]"
      >
        <span class="-rotate-90">
          <TranslationComp text="Floor" />
        </span>
        <svg
          :class="[( floorsMobileViewToggle ? 'rotate-0' : 'rotate-180' ),'fill-secondaryText w-[6px] h-3 transition-transform duration-500 ease-[cubic-bezier(0.165, 0.84, 0.44, 1)]']"
          viewBox="0 0 6 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.856844 12C0.63735 12 0.418064 11.9026 0.25109 11.7068C-0.0836966 11.316 -0.0836966 10.6827 0.25109 10.2921L3.93081 5.99927L0.25109 1.70762C-0.0836966 1.31679 -0.0836966 0.683538 0.25109 0.292945C0.585876 -0.0976483 1.12865 -0.0976483 1.46364 0.292945L5.74891 5.29254C6.0837 5.68313 6.0837 6.31638 5.74891 6.70722L1.46364 11.7068C1.29562 11.9026 1.07613 12 0.856844 12Z"
            fill=""
          />
        </svg>
      </button>
      <FloorSwitchSlider
        :per-slide-view="Store.isLandscape ? 3: 5"
        :class="[(floorsMobileViewToggle ? (Store.isLandscape ? '-left-[109px] -top-[27px] -rotate-90' : 'right-0' ): '-right-[200px] '),(Store.isLandscape ? 'w-[5.48rem]':'w-[2.12rem]' ), 'fixed h-fit transition-[right] duration-500 ease-[cubic-bezier(0.165, 0.84, 0.44, 1)']"
        :number-of-floors="sortedFloors"
        :default-id="null"
        :enabledFloors="floorSwitchEnableIds"
        :activeIndicatorLine="false"
        @handle-selection="handleFloorSwitch"
      />
    </div>

    <!-- <div class="absolute right-4 top-28 mt-8">
      <ControlButton v-if="Store.showFilter && Store.isMobile" :is-hover="showTooltip.includes('filter')"
        :inactive-s-v-g="toolBtnData.filterInactiveSVG" :active-s-v-g="toolBtnData.filterActiveSVG" class="!m-0"
        :class="isMobile ? 'h-10 w-10 p-' : ''" tooltiptext="Apply Filters"
        @on-click="onClickButton(&quot;filter&quot;)" @mouseout="removeItem('filter')" />
    </div>
    <div
      v-if="!Store.isMobile"
      class="absolute z-1 bottom-0 right-4"
    >
      <FilterButton
        ref="filterBarBtn"
        message="Filters"
        :isToggle="filterBtnToggle"
        @handle-button-click="showModal"
      />
      <FilterBar
        ref="filterBar"
        :openModal="isShowModal"
        :showFloors="true"
        @close-modal="closeFilterModal"
        @show-results="handleResults"
        @clear-filter="handleClear"
        @close-on-error="handleSearchError"
      />
      <Toast
        v-if="searchResultToast"
        position="bottom-center"
        size="lg"
        :autoClose="true"
        :duration="5"
        @update:show-toast="closeToast"
      >
        <template #content>
          <div class="h-7 flex items-center gap-4">
            <span class="w-5">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="information-circle">
                  <path
                    id="Vector"
                    d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
                    stroke="white"
                    stroke-width="1.5"
                  />
                  <path
                    id="Vector_2"
                    d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
                    stroke="white"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector_3"
                    d="M11.9922 8H12.0012"
                    stroke="white"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
              </svg>
            </span>
            <span
              class="text-white pl-4 border-l border-gray-200 text-base font-normal font-['Inter'] leading-normal "
            >Not
              Available</span>
          </div>
        </template>
      </Toast>
    </div>
    <div v-else class="absolute z-10 bottom-0 w-full">
      <FilterBar :openModal="openFilterModal" :showFloors="true" @close-modal="() => openFilterModal = false"
        @show-results="handleResults" @clear-filter="handleClear" @close-on-error="handleSearchError" />
      <Toast v-if="searchResultToast" position="bottom-center" size="lg" :autoClose="true" :duration="5"
        @update:show-toast="closeToast">
        <template #content>
          <div class="h-7 flex items-center gap-4">
            <span class="w-5">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="information-circle">
                  <path id="Vector"
                    d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
                    stroke="white" stroke-width="1.5" />
                  <path id="Vector_2"
                    d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
                    stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <path id="Vector_3" d="M11.9922 8H12.0012" stroke="white" stroke-width="2" stroke-linecap="round"
                    stroke-linejoin="round" />
                </g>
              </svg>
            </span>
            <span
              class="text-white pl-4 border-l border-gray-200 text-base font-normal font-['Inter'] leading-normal ">Not
              Available</span>
          </div>
        </template>
      </Toast>
    </div> -->

    <!-- <SideBar
      v-if="!Store.isMobile && Store.allUnitCardData && Object.keys(Store.allUnitCardData).length>0"
      title="Inventory"
      class="text-base font-medium whitespace-nowrap"
    >
      <template #content>
        <SearchComponent
          v-if="Store.allUnitCardData"
          :data="Store.allUnitCardData"
          :buildingData="Store.buildingData"
          :hideStatus="Store.hideStatus"
          :favUnits="Store.favoritesData"
          @goto-unit="gotoUnit"
          @toggle-button="Store.updateAddRemovefavorites"
        />
      </template>
    </SideBar> -->
  </portal>
</template>
<style scoped>
.pop
{
  animation:pop 0.3s cubic-bezier( 0.6, 0.26, 0.57, 0.88 );
}
@keyframes pop
{
  from
  {
  opacity:0;
  scale:0.6
  }

}

@media screen and (max-width: 430px) {
.slide-animation
{
  animation:popup 0.5s cubic-bezier(0.45, 0.05, 0.55, 0.95) forwards;
}
}

@keyframes popup
{
  0%
  {
    bottom :-24em;
  }
  100%
  {
    @apply md:bottom-0 ;
  }
}

.slide-in-blurred-bottom {
  animation: slide-in-blurred-bottom 0.6s cubic-bezier(0.230, 1.000, 0.320, 1.000) both;
}

@keyframes slide-in-blurred-bottom {
  0% {
    -webkit-transform: translateY(1000px) ;
            transform: translateY(1000px);

    -webkit-filter: blur(40px);
            filter: blur(40px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0) ;
            transform: translateY(0) ;

    -webkit-filter: blur(0);
            filter: blur(0);
    opacity: 1;
  }
}

</style>
