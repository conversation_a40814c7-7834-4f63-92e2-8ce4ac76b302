<!-- unitsLayer.vue -->
<script setup>
import { defineProps, onUnmounted, ref, watch, onMounted, computed, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { cardPosition } from '../../../helpers/cardPosition';
// Import UnitHeader  from '../../ALEComponents/UnitHeader/UnitHeader.vue';
// Import FloorPlateCard from '../../ALEComponents/FloorPlateCard/FloorPlateCard.vue';
import FloorPlanCard from '../../ALEComponents/FloorPlanCard/FloorPlanCard.vue';
import { creationToolStore } from '../../../store/index';
import BedroomLegend from '../../ALEComponents/BedroomLegend/BedroomLegend.vue';
import FloorSwitch from '../../ALEComponents/FloorSwitch/FloorSwitch.vue';
import FloorSwitchSlider from '../../ALEComponents/FloorSwitchSlider/FloorSwitchSlider.vue';
import router from '../../../router';
import { findCenterOfDiv, Googleanalytics, addSVGDeepZoom, setActiveElem, cdn, formatBedrooms } from '../../../helpers/helper';
import OpenSeadragon from 'openseadragon';
import UnitLabel from '../../ALEComponents/UnitLabel/UnitLabel.vue';
import FilterBar from '../../ALEComponents/FilterBar/FilterBar.vue';
import FilterButton from '../../ALEComponents/FilterButton/FilterButtonComponent.vue';
import Toast from "../../Overall/Toast/CustomToast.vue";
import ControlButton from '../../ALEComponents/ControlButton/ControlButton.vue';
import { isRealValue } from '../../../helpers/helper';
import { onClickOutside } from '@vueuse/core';
import TranslationComp from '../../ALEComponents/TranslationComp/TranslationComp.vue';
import { availUnitOrgs } from '../../../config/masterdata';
const route = useRoute();
var showTooltip = ref([]);
const props = defineProps({
  layerData: {
    type: Object,
    default () {
      return {};
    },
  },
  sceneType: {
    type: String,
    default: '',
  },
},

);
const organisationId = ref(route.params.organizationId);
const projectId = ref(route.params.projectId);
const Store = creationToolStore();
Store.showFilter = true;
const floorsMobileViewToggle = ref(false);
const on_pan = ref(true);
const unirefs = ref([]);
const clickedId = ref(false), showSVG = ref(false), showLabel= ref(false), unitplanId = ref(false), unitId = ref(false), showUnitCard = ref(false), position = ref(false);
const rendered_layerData = ref({}), currentUnits = ref([]), unitSVGElem = ref({}), unitLabelElem = ref({});
const startTime = ref(new Date), timespent = ref(0), list = ref();
const buildingId = ref(route.query.building_id);
const isShowModal = ref(false);
const filterBtnToggle = ref(false);
const filterBar = ref();
const filterBarBtn = ref();
const layersId = ref(new Set());
const searchResultToast = ref(false);
const projectTheme = ref(null);
const openFilterModal = ref(false);
const toastRef = ref();
const emptyFilterBar = ref(false);
const clearFilterRef = ref(false), isAndriod = ref();
// let mouseTracker;
const toolBtnData = ref({
  filterActiveSVG: `<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.68595 9.95455L14.1303 17.1364V20.7727L17.8697 23.5V17.1364L23.3141 9.95455C23.4178 9.81924 23.481 9.6583 23.4963 9.48983C23.5117 9.32136 23.4787 9.15204 23.401 9.00091C23.3233 8.84978 23.2041 8.72283 23.0567 8.63434C22.9093 8.54585 22.7395 8.49933 22.5666 8.50001H9.43344C9.26046 8.49933 9.09073 8.54585 8.94331 8.63434C8.7959 8.72283 8.67665 8.84978 8.59899 9.00091C8.52132 9.15204 8.48831 9.32136 8.50367 9.48983C8.51904 9.6583 8.58216 9.81924 8.68595 9.95455Z" fill="white"/>
</svg>
`,
  filterInactiveSVG: `<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.68595 9.95455L14.1303 17.1364V20.7727L17.8697 23.5V17.1364L23.3141 9.95455C23.4178 9.81924 23.481 9.6583 23.4963 9.48983C23.5117 9.32136 23.4787 9.15204 23.401 9.00091C23.3233 8.84978 23.2041 8.72283 23.0567 8.63434C22.9093 8.54585 22.7395 8.49933 22.5666 8.50001H9.43344C9.26046 8.49933 9.09073 8.54585 8.94331 8.63434C8.7959 8.72283 8.67665 8.84978 8.59899 9.00091C8.52132 9.15204 8.48831 9.32136 8.50367 9.48983C8.51904 9.6583 8.58216 9.81924 8.68595 9.95455Z" fill="#111928"/>
</svg>
`,
});
// Computed property to sort floors by 'order'
const sortedFloors = computed(() => {
  return Object.values(Store.allBuildings[buildingId.value].floors)
    .sort((a, b) => a.order - b.order);
});
const onClickButton = (id) => {
  if (id === 'filter') {
    openFilterModal.value = true;
  }
};
if (Store.projectCardData[route.params.projectId].projectSettings.theme) {
  projectTheme.value = Store.projectCardData[route.params.projectId].projectSettings.theme.theme;
} else {
  projectTheme.value = Store.organizationDetails.theme;
}
if (buildingId.value && Store.floorData[buildingId.value]) {
  Store.enabledFloors = Object.keys(Store.floorData[buildingId.value]).map((key) => {
    const floors = Store.floorData[buildingId.value][key]?.floors;
    return floors ? floors : null;
  });
}
function handleEmptyFilter (boolValue) {
  if (boolValue) {
    emptyFilterBar.value = boolValue;
  }
}
function findFirstWithUnits (objects) {
  return Object.values(objects).find(
    (obj) => obj.layer_data && Array.isArray(obj.layer_data.units),
  ) || null;
}

const createQueryParams = (query) => {
  const { status, min_price, max_price, min_area, max_area, unitplan_type, style_types, bedrooms, community_id } = query;

  const apiParams = {};
  if (status) {
    apiParams.status = status;
  }
  if (min_area) {
    apiParams.min_area = min_area;
  }
  if (max_area) {
    apiParams.max_area = max_area;
  }
  if (min_price) {
    apiParams.min_price = min_price;
  }
  if (max_price) {
    apiParams.max_price = max_price;
  }
  if (unitplan_type) {
    apiParams.unitplan_type = unitplan_type;
  }
  if (style_types) {
    apiParams.style_types = style_types;
  }
  if (bedrooms) {
    apiParams.bedrooms = bedrooms;
  }
  if (community_id){
    apiParams.community_id = community_id;
  }

  return apiParams;
};

function fetchFilteredUnits (params) {
  if (Object.keys(params).length === 0) {
    return Promise.resolve();
  }
  return Store.getFilteredUnits(organisationId.value, projectId.value, params);
}

function extractAllLayerId (data) {
  let allLayers = [];
  for (const key in data) {
    allLayers = allLayers.concat(data[key].layer_data.layer_id);
  }
  return allLayers;
}

function updateDOMElements (layerData, layersId) {
  if (layersId.size === 0) {
    return;
  }
  const elementValues = extractAllLayerId(layerData);

  elementValues.forEach((elementValue) => {
    if (!layersId.has(elementValue)) {
      const elementLayerId = props.sceneType === 'deep_zoom'
        ? (unitSVGElem.value &&
          unitSVGElem.value[elementValue] &&
          unitSVGElem.value[elementValue].g)
          ? unitSVGElem.value[elementValue].g
          : null
        : document.getElementById(elementValue);

      if (elementLayerId) {
        elementLayerId.style.fillOpacity = '0';
        elementLayerId.style.pointerEvents = 'none';
        elementLayerId.style.display = 'none';
        document.getElementById(`${elementValue}label`).classList.add('invisible');
      }
    }
  });
}

function extractUnitsWithLayerId (data) {
  const unitsWithLayerId = [];
  for (const key in data) {
    const layerData = data[key].layer_data;
    const layerId = layerData.layer_id;
    const units = layerData.units;
    units.forEach((unit) => {
      unitsWithLayerId.push({ unit, layerId });
    });
  }
  return unitsWithLayerId;
}

function getUnitBedrooms (id) {
  if (id) {
    if (Object.keys(Store.allUnitCardData).length > 0) {
      // return Store.allUnitCardData[id].bedroom === 3.5 ? 'fill-unit-b3.5-fill stroke-unit-b3.5-fill ' : Store.allUnitCardData[id].bedroom === 3 ? 'fill-unit-b3-fill stroke-unit-b3-stroke ' : Store.allUnitCardData[id].bedroom === 2.5 ? 'fill-unit-b2.5-fill stroke-unit-b2.5-fill ' : Store.allUnitCardData[id].bedroom === 2 ? 'fill-unit-b2-fill stroke-unit-b2-fill ' : Store.allUnitCardData[id].bedroom === 1 ? 'fill-unit-b1-fill stroke-unit-b1-stroke ' : Store.allUnitCardData[id].bedroom === 4 ? 'fill-unit-b4-fill stroke-unit-b4-fill ' : Store.allUnitCardData[id].bedroom === 5 ? 'fill-unit-b5-fill stroke-unit-b5-fill' : Store.allUnitCardData[id].bedroom === 6 ? 'fill-unit-b6-fill stroke-unit-b6-fill' : Store.allUnitCardData[id].type === 'studio' ? 'fill-unit-s1-fill stroke-unit-s1-fill ' : 'fill-black stroke-gray-600 opacity-40';
      return Store.allUnitCardData[id]?.bedroom === '3.5BHK' ? 'fill-unit-b3.5-fill stroke-unit-b3.5-fill' : Store.allUnitCardData[id]?.bedroom === '3BHK' ? 'fill-unit-b3-fill stroke-unit-b3-fill' : Store.allUnitCardData[id]?.bedroom === '2.5BHK' ? 'fill-unit-b2.5-fill stroke-unit-b2.5-fill' : Store.allUnitCardData[id]?.bedroom === '2BHK' ? 'fill-unit-b2-fill stroke-unit-b2-fill' : Store.allUnitCardData[id]?.bedroom === '1BHK' ? 'fill-unit-b1-fill stroke-unit-b1-fill' : Store.allUnitCardData[id]?.bedroom === '4BHK' ? 'fill-unit-b4-fill stroke-unit-b4-fill' : (Store.allUnitCardData[id]?.bedroom === '5BHK' || Store.allUnitCardData[id]?.bedroom === 'suite') ? 'fill-unit-b5-fill stroke-unit-b5-fill' : (Store.allUnitCardData[id]?.bedroom === '6BHK' || Store.allUnitCardData[id]?.bedroom === 'penthouse') ? 'fill-unit-b6-fill stroke-unit-b6-fill' : (Store.allUnitCardData[id]?.bedroom === 'studio' || Store.allUnitCardData[id]?.bedroom === 'duplex') ? 'fill-unit-s1-fill stroke-unit-s1-fill' : (Store.allUnitCardData[id]?.bedroom === '7BHK' || Store.allUnitCardData[id]?.bedroom === 'townhouse') ? 'fill-unit-b7-fill stroke-unit-b7-fill' : 'fill-black stroke-gray-600 opacity-40';
    }
    return null;
  }
  return null;
}

function updateDOMElementsOnError (layerData) {
  const elementValues = extractAllLayerId(layerData);
  elementValues.forEach((elementValue) => {
    const elementLayerId = props.sceneType === 'deep_zoom'
      ? (unitSVGElem.value &&
          unitSVGElem.value[elementValue] &&
          unitSVGElem.value[elementValue].g)
        ? unitSVGElem.value[elementValue].g
        : null
      : document.getElementById(elementValue);

    if (elementLayerId) {
      elementLayerId.style.fillOpacity = '0';
      elementLayerId.style.pointerEvents = 'none';
      elementLayerId.style.display = 'none';
      document.getElementById(`${elementValue}label`).classList.add('invisible');
    }
  });
}

function handleClearOnError () {
  const elementValues = extractUnitsWithLayerId(props.layerData);
  elementValues.forEach((elementValue) => {
    const bedRoomColor = getUnitBedrooms(elementValue.unit);
    const elementId = props.sceneType === 'deep_zoom'
      ? (unitSVGElem.value &&
        unitSVGElem.value[elementValue.layerId] &&
        unitSVGElem.value[elementValue.layerId].g)
        ? unitSVGElem.value[elementValue.layerId].g
        : null
      : document.getElementById(elementValue.layerId);
    if (elementId && bedRoomColor !== null && bedRoomColor) {
      elementId.style = `${bedRoomColor}`;

      if (props.sceneType !== 'deep_zoom') {
        elementId.style.fillOpacity = '0.4';
      }
      document.getElementById(`${elementValue.layerId}label`).classList.remove('invisible');
    }
  });
  clearFilterRef.value = false;
}

function handleSearchError () {
  openFilterModal.value = false;
  searchResultToast.value = true;
  clearFilterRef.value = true;
  if (clearFilterRef.value) {
    updateDOMElementsOnError(props.layerData);
  }
}

function handleResults (params) {

  let newQuery;
  if (Object.keys(params).length > 0) {
    newQuery = { ...route.query };

    Object.keys(params).forEach((key) => {
      newQuery[key] = params[key];
    });

    router.replace({ name: route.name, query: newQuery });
  }
  /* eslint-disable-next-line no-unused-vars */
  const { building_id, floor_id, community_id, ...filteredParams } = newQuery || {};

  const queryParams = new URLSearchParams(filteredParams).toString();

  fetchFilteredUnits(queryParams)
    .then(() => {
      updateDOMElements(props.layerData, layersId.value);
    })
    .catch((err) => {
      if (err) {
        handleSearchError();
      }
    });
}

if (!route.query.community_id && !route.query.building_id && !route.query.floor_id) {
  // If not there
  const layerdata = findFirstWithUnits(props.layerData);
  const uniqueCommunityId = ref(layerdata ? Store.allUnitCardData[layerdata.layer_data.units[0]].community_id : null); // get the community id
  if (uniqueCommunityId.value) {
    router.replace({ name: "projectScene", query: { ...route.query, community_id: uniqueCommunityId.value, status: availUnitOrgs.includes(route.params.organizationId)?"available":'' } }); // set the route
    const queryObj = {
      community_id: uniqueCommunityId.value,
      status: availUnitOrgs.includes(route.params.organizationId)?'available':"",
    }; // frame the query Obj
    const queryParams = createQueryParams(queryObj);
    handleResults(queryParams); // call the api
  }

}

watch(Store.floorData, () => {
  if (buildingId.value && Store.floorData[buildingId.value]) {
    Store.enabledFloors = Object.keys(Store.floorData[buildingId.value]).map((key) => {
      const floors = Store.floorData[buildingId.value][key]?.floors;
      return floors ? floors : null;
    });
  }
});

function showModal () {
  filterBtnToggle.value = !filterBtnToggle.value;
  isShowModal.value = !isShowModal.value;
}

function closeFilterModal () {
  filterBtnToggle.value = false;
  isShowModal.value = false;
  openFilterModal.value = false;
}

function findLayerIdByUnitId (data, unitId) {
  for (const key in data) {
    if (props.sceneType === 'deep_zoom') {
      if (data[key].units.includes(unitId)) {
        return key;
      }
    } else {
      if (data[key].layer_data.units.includes(unitId) && route.query.floor_id && (Store.allUnitCardData[unitId].floor_id === route.query.floor_id)) {
        return data[key].layer_data.layer_id;
      }

    }
  }
  return null;
}

function closeToast () {
  searchResultToast.value = false;
}

window.addEventListener('popstate', () => { });

function handleClear () {
  const elementValues = extractUnitsWithLayerId(props.layerData).filter((elementValue) => {
    return !layersId.value.has(elementValue.layerId);
  });
  elementValues.forEach((elementValue) => {
    const bedRoomColor = getUnitBedrooms(elementValue.unit);
    const elementId = props.sceneType === 'deep_zoom'
      ? (unitSVGElem.value &&
        unitSVGElem.value[elementValue.layerId] &&
        unitSVGElem.value[elementValue.layerId].g)
        ? unitSVGElem.value[elementValue.layerId].g
        : null
      : document.getElementById(elementValue.layerId);
    if (!availUnitOrgs.includes(route.params.organizationId)) {
      if (elementId && bedRoomColor !== null && bedRoomColor) {
        elementId.style = `${bedRoomColor}`;

        if (props.sceneType !== 'deep_zoom') {
          elementId.style.fillOpacity = '0.4';
        }
        document.getElementById(`${elementValue.layerId}label`).classList.remove('invisible');
      }
    } else {
      if (Store.unitData[elementValue.unit] && Store.unitData[elementValue.unit].status === 'available') {
        if (elementId && bedRoomColor !== null && bedRoomColor) {
          elementId.style = `${bedRoomColor}`;

          if (props.sceneType !== 'deep_zoom') {
            elementId.style.fillOpacity = '0.4';
          }
          document.getElementById(`${elementValue.layerId}label`).classList.remove('invisible');
        }
      }
    }
  });
}

watch(() => Store.filteredUnits, () => {
  if (!isRealValue(Store.filteredUnits)) {
    return;
  }
  const layersSet = new Set();

  for (const filteredUnit of Object.values(Store.filteredUnits)) {
    const unit_id = filteredUnit._id;

    if (unit_id) {
      const layer_id = props.sceneType === 'deep_zoom' ? findLayerIdByUnitId(unitSVGElem.value, unit_id) : findLayerIdByUnitId(props.layerData, unit_id);
      if (layer_id !== undefined && layer_id !== null) {
        layersSet.add(layer_id);
      }
    }
  }

  if (layersSet.size !== 0) {
    handleClearOnError();
    handleClear();
    layersId.value = layersSet;
  }
});

function removeItem (id) {
  const index = showTooltip.value.findIndex((item) => item === id);

  if (index !== -1) {
    showTooltip.value.splice(index, 1);
  }
}

function zoomFun (id) {
  var svgGroup = document.getElementById(id);
  // Get the bounding rectangle of the SVG group
  var rect = svgGroup.getBoundingClientRect();
  // Define animation parameters
  var animationTime = 300; // Ms
  var animationStepTime = 15;
  var animationSteps = animationTime / animationStepTime;
  var animationStep = 0;
  if (window.panzoomer) {
    // Define the duration of the animation in milliseconds
    const duration = 1000; // You can adjust this value as needed
    const targetZoom = 1.75;

    // Calculate the difference between current and target zoom levels
    const currentZoom = window.panzoomer.getZoom();
    const zoomDifference = targetZoom - currentZoom;
    // Calculate the increment per frame for zooming
    const incrementPerFrame = zoomDifference / (duration / (1000 / 60)); // Assuming 60 FPS

    // Calculate initial pan amounts
    const initialRectYPercent = (window.innerHeight / 2) - rect.top + (rect.height / 2) - rect.height;
    const initialRectXPercent = (window.innerWidth / 2) - rect.left + (rect.width / 2) - rect.width;
    let stepX = initialRectXPercent / animationSteps;
    let stepY = initialRectYPercent / animationSteps;

    // Record the start time of the animation
    const startTime = performance.now();

    // Define the animation function
    const animate = (timestamp) => {
      // Calculate the time elapsed since the animation started
      const elapsed = timestamp - startTime;

      // Update the current zoom level
      let newZoom = currentZoom + (incrementPerFrame * elapsed);
      if (zoomDifference > 0) {
        newZoom = Math.min(newZoom, targetZoom);
      } else {
        newZoom = Math.max(newZoom, targetZoom);
      }
      window.panzoomer.zoomAtPoint(newZoom, { x: rect.left, y: rect.top });

      // Recalculate pan amounts at each step
      const rectYPercent = (window.innerHeight / 2) - rect.top + (rect.height / 2) - rect.height;
      const rectXPercent = (window.innerWidth / 2) - rect.left + (rect.width / 2) - rect.width;
      stepX = rectXPercent / animationSteps;
      stepY = rectYPercent / animationSteps;

      // Pan the SVG group
      if (animationStep++ < animationSteps && rectXPercent !== 0) {
        window.panzoomer.panBy({ x: stepX, y: stepY });
        // Request next animation frame
        requestAnimationFrame(animate);
      } else {
        // If animation is complete, set the final zoom level
        window.panzoomer.zoomAtPoint(targetZoom, { x: rect.left, y: rect.top });
      }
    };

    // Start the animation
    requestAnimationFrame(animate);
  }
}

async function setRenderData (Currentfloor_id) {
  rendered_layerData.value = {};
  // Rendered_layerData.value=props.layerData
  Object.keys(props.layerData).forEach((layerIndex) => {
    const layer = props.layerData[layerIndex];
    Object.values(layer.layer_data.units).forEach((unit) => {
      if (Currentfloor_id && Store.unitData[unit] && Store.unitData[unit].floor_id && Store.unitData[unit].floor_id === Currentfloor_id) {
        if (availUnitOrgs.includes(route.params.organizationId)) {
          if (Store.unitData[unit].status === "available") {
            if (!rendered_layerData.value[layerIndex]) {
              rendered_layerData.value[layerIndex] = {};
            }
            rendered_layerData.value[layerIndex].unit = Store.unitData[unit];
            currentUnits.value.push(Store.unitData[unit]);
            rendered_layerData.value[layerIndex].layer_data = layer.layer_data;
            rendered_layerData.value[layerIndex].layer = layer.layer;
          }
        } else {
          if (!rendered_layerData.value[layerIndex]) {
            rendered_layerData.value[layerIndex] = {};
          }
          rendered_layerData.value[layerIndex].unit = Store.unitData[unit];
          currentUnits.value.push(Store.unitData[unit]);
          rendered_layerData.value[layerIndex].layer_data = layer.layer_data;
          rendered_layerData.value[layerIndex].layer = layer.layer;
        }

      } else if (Store.unitData[unit] && !Currentfloor_id) {
        rendered_layerData.value[layerIndex] = {};
        rendered_layerData.value[layerIndex].unit = Store.unitData[unit];
        currentUnits.value.push(Store.unitData[unit]);
        rendered_layerData.value[layerIndex].layer_data = layer.layer_data;
        rendered_layerData.value[layerIndex].layer = layer.layer;
      }
    });
  });
}
// const computedThemeClass = computed(() => {
//   switch (projectTheme.value) {
//     case 'light':
//       return 'theme-light';
//     case 'dark':
//       return 'theme-dark';
//     case 'custom':
//       return 'theme-custom';
//     default:
//       return 'theme-dark';
//   }
// });
function getUnitData (unit) {
  if (unit) {
    if (Object.keys(Store.allUnitCardData).length > 0) {
      return Store.allUnitCardData[unit._id].bedroom === '3.5BHK' ? 'fill-unit-b3.5-fill stroke-unit-b3.5-fill' : Store.allUnitCardData[unit._id].bedroom === '3BHK' ? 'fill-unit-b3-fill stroke-unit-b3-fill' : Store.allUnitCardData[unit._id].bedroom === '2.5BHK' ? 'fill-unit-b2.5-fill stroke-unit-b2.5-fill' : Store.allUnitCardData[unit._id].bedroom === '2BHK' ? 'fill-unit-b2-fill stroke-unit-b2-fill' : Store.allUnitCardData[unit._id].bedroom === '1BHK' ? 'fill-unit-b1-fill stroke-unit-b1-fill' : Store.allUnitCardData[unit._id].bedroom === '4BHK' ? 'fill-unit-b4-fill stroke-unit-b4-fill' : (Store.allUnitCardData[unit._id].bedroom === '5BHK' || Store.allUnitCardData[unit._id].bedroom === 'suite') ? 'fill-unit-b5-fill stroke-unit-b5-fill' : (Store.allUnitCardData[unit._id].bedroom === '6BHK' || Store.allUnitCardData[unit._id].bedroom === 'penthouse') ? 'fill-unit-b6-fill stroke-unit-b6-fill' : (Store.allUnitCardData[unit._id]?.bedroom === 'studio' || Store.allUnitCardData[unit._id]?.bedroom === 'duplex') ? 'fill-unit-s1-fill stroke-unit-s1-fill' : (Store.allUnitCardData[unit._id].bedroom === '7BHK' || Store.allUnitCardData[unit._id].bedroom === 'townhouse')? 'fill-unit-b7-fill stroke-unit-b7-fill' : 'fill-black stroke-gray-600 opacity-40';
    }
    return null;
  }
  return null;

}
function moveToNxtScene (id, unit_id, event) {
  var svgGroup = document.getElementById(id);
  var rect = svgGroup.getBoundingClientRect();
  if (Store.isMobile) {
    zoomFun(id);
  }
  const cursorX = event.clientX;
  const cursorY = event.clientY;
  const cardWidth = 300;
  const cardHeight = 60;
  const newPosition = cardPosition(rect, cursorX, cursorY, cardWidth, cardHeight);
  position.value = newPosition;
  unitId.value = unit_id;
  unitplanId.value = Store.unitData[unitId.value].unitplan_id;
  clickedId.value = id;
  // window.navigator.vibrate(200);  // Haptic vibration
  showUnitCard.value = true;
}
if (props.sceneType === 'deep_zoom') {
  window.viewer.addHandler('canvas-drag', () => {
    if (clickedId.value) {
      const unitElem = unitSVGElem.value[clickedId.value].g;
      unitElem.classList.add('opacity-30');
      unitElem.classList.remove('transparent', 'opacity-70', '!fill-[#ffffff00]');
    }
    if (showUnitCard.value) {
      showUnitCard.value = false;
    }
  });
}
function setSVG () {
  if (Object.values(rendered_layerData.value).length > 0) {
    Object.values(rendered_layerData.value).forEach(async (item) => {
      const requestOptions = {
        method: "GET",
        redirect: "follow",
      };
      const response = await fetch(cdn(item.layer), requestOptions);
      const svgString = await response.text();
      const obj = addSVGDeepZoom({
        g: svgString,
        zIndex: item.layer_data.zIndex,
        reSize: item.layer_data.reSize,
        x: item.layer_data.x,
        y: item.layer_data.y,
        width: item.layer_data.width,
        height: item.layer_data.height,
        placement: item.layer_data.placement,
        layer_id: item.layer_data.layer_id,
      }, window.viewer);
      const svgElement = document.createElement("span");
      svgElement.classList.add("font-medium", "rounded", "z-[1]",  route.params.organizationId, rendered_layerData.value[item.layer_data.layer_id].unit.status);
      svgElement.classList.add("pointer-events-none", 'text-[10px]', 'p-[3px]', 'text-secondaryText', 'bg-secondary', "opacity-75");
      svgElement.id = item.layer_data.layer_id + 'label';
      svgElement.innerHTML = rendered_layerData.value[item.layer_data.layer_id].unit ? rendered_layerData.value[item.layer_data.layer_id].unit.name : '';
      // Calculate the center coordinates of the existing element
      const centerX = item.layer_data.x + (item.layer_data.width / 2);
      const centerY = item.layer_data.y + (item.layer_data.height / 2);
      window.viewer.addOverlay({
        element: svgElement,
        px: centerX,
        py: centerY,
        placement: "CENTER",
        checkResize: false,
      });
      obj.svgElement.children[0].classList.add('!cursor-pointer', 'opacity-30', 'stroke-[0.2rem]', route.params.organizationId, rendered_layerData.value[item.layer_data.layer_id].unit.status);
      obj.svgElement.children[0].setAttribute("label", rendered_layerData.value[item.layer_data.layer_id].unit ? rendered_layerData.value[item.layer_data.layer_id].unit.name : '');
      unitSVGElem.value[item.layer_data.layer_id] = { 'g': obj.svgElement.children[0], 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel, 'units': item.layer_data.units };
      unitLabelElem.value[item.layer_data.layer_id] = { 'g': svgElement, 'minZoomLevel': item.layer_data.minZoomLevel, 'maxZoomLevel': item.layer_data.maxZoomLevel, 'units': item.layer_data.units };
      // SVG toggle logic for initial visibility
      if (Store.svgVisibility.showSVG) {
        obj.svgElement.children[0].classList.remove('!hidden');
        svgElement.classList.remove('!hidden');
      } else {
        obj.svgElement.children[0].classList.add('!hidden');
        svgElement.classList.add('!hidden');
      }
      if (rendered_layerData.value[item.layer_data.layer_id].unit) {
        const classes = getUnitData(rendered_layerData.value[item.layer_data.layer_id].unit);
        const importantClasses = classes.split(' ');
        importantClasses.forEach((className) => {
          obj.svgElement.children[0].classList.add(className);
        });
      } else {
        obj.svgElement.children[0].classList.add('fill-none');
      }

      if (item.layer_data.minZoomLevel && item.layer_data.maxZoomLevel) {
        if (Store.currentZoomlevel >= item.layer_data.minZoomLevel
          && Store.currentZoomlevel <= item.layer_data.maxZoomLevel) {
          obj.svgElement.children[0].classList.add('!visible');
          obj.svgElement.children[0].classList.remove('!hidden');
          svgElement.classList.add('!visible');
          svgElement.classList.remove('!hidden');
        } else {
          obj.svgElement.children[0].classList.remove('!visible');
          obj.svgElement.children[0].classList.add('!hidden');
          svgElement.classList.remove('!visible');
          svgElement.classList.add('!hidden');
        }
      }
      new OpenSeadragon.MouseTracker({
        element: obj.svgElement.children[0],
        clickHandler: function (event) {

          // Check if a new layer was clicked
          if (item.layer_data.layer_id !== clickedId.value) {
            if (clickedId.value) {
              unitSVGElem.value[clickedId.value].g.classList.add('opacity-30');
              unitSVGElem.value[clickedId.value].g.classList.remove('transparent', 'opacity-70', '!fill-[#ffffff00]');
            }
            showUnitCard.value = false;
            clickedId.value = item.layer_data.layer_id;
            obj.svgElement.children[0].classList.add('!fill-[#ffffff00]');

            const viewport = window.viewer.viewport;
            const viewerRect = window.viewer.element.getBoundingClientRect();
            const overlayRect = obj.svgElement.children[0].getBoundingClientRect(); // Get the element position and sizings

            // Calculate the center of the overlay in screen coordinates
            const overlayCenterX = overlayRect.left + (overlayRect.width / 2); // X
            const overlayCenterY = overlayRect.top + (overlayRect.height / 2); // Y

            // Convert the screen coordinates to viewport coordinates
            const viewportPoint = viewport.pointFromPixel(
              new OpenSeadragon.Point(
                overlayCenterX - viewerRect.left,
                overlayCenterY - viewerRect.top,
              ),
            );

            // Get the current center of the viewport
            const currentViewportCenter = viewport.getCenter();
            // Round the x and y values of both points to one decimal place for comparison
            const isSamePoint = (Math.round(currentViewportCenter.x * 10) / 10 === Math.round(viewportPoint.x * 10) / 10) &&
              (Math.round(currentViewportCenter.y * 10) / 10 === Math.round(viewportPoint.y * 10) / 10);

            // Check if the clicked point is already at the center of the viewport
            if (isSamePoint) {
              if (Store.currentZoomlevel < 2.5) {
                const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2
                // Zoom in to the calculated viewport point
                viewport.zoomTo(zoomLevel, viewportPoint);
              } else {
                moveToNxtScene(item.layer_data.layer_id, rendered_layerData.value[item.layer_data.layer_id].unit._id, event);
                return;
              }
            }

            // Check if zooming is required
            if (!showUnitCard.value && Store.currentZoomlevel < 2.5) {
              // Define the zoom level you want to zoom into
              const zoomLevel = Math.round(viewport.getZoom()) * 2.5; // Example: zoom in by a factor of 2

              // Zoom in to the calculated viewport point
              viewport.zoomTo(zoomLevel, viewportPoint);
            } else {
              // Pan to the clicked point
              viewport.panTo(viewportPoint);

              // Constrain the bounds to prevent panning beyond the image
              viewport.applyConstraints();
            }

            // Call moveToNxtScene after the animation finishes (zoom or pan)
            window.viewer.addOnceHandler('animation-finish', function () {
              moveToNxtScene(item.layer_data.layer_id, rendered_layerData.value[item.layer_data.layer_id].unit._id, event);
            });
          }
        },
      });

      obj.svgElement.children[0].addEventListener("mouseenter", () => {
        if (clickedId.value !== item.layer_data.layer_id && !clickedId.value) {
          obj.svgElement.children[0].classList.remove('opacity-30');
          obj.svgElement.children[0].classList.add('stroke-[0.2rem]', 'opacity-70');
        }
      },
      );
      obj.svgElement.children[0].addEventListener("mouseleave", () => {
        if (clickedId.value !== item.layer_data.layer_id && !clickedId.value) {
          obj.svgElement.children[0].classList.add('opacity-30');
          obj.svgElement.children[0].classList.remove('transparent', 'opacity-70', '!fill-[#ffffff00]');
        }
      },
      );
    });
  }
}
setRenderData(route.query.floor_id).then(() => {
  if (props.sceneType === 'deep_zoom') {
    setSVG();
  }
  if (!showSVG.value){
    showSVG.value = true;
    setTimeout(() => {
      showLabel.value=true;
    }, 100);
  }
});
document.addEventListener('hammer_panstart_panmove', () => {
  on_pan.value = false;
  setTimeout(() => {
    on_pan.value = true;

  }, 750);
});
function closeModal () {
  showUnitCard.value = false;
  clickedId.value = false;
}

const buildAnalyticsFields = () => {
  const fields = {
    organization_id: route.params.organizationId,
    organization_name: Store.organizationDetails?.name,
    project_id: route.params.projectId,
    project_name: Store.projectCardData?.[route.params.projectId]?.name,
    project_city: Store.projectCardData[route.params.projectId].city,
    project_type: buildingId.value ? Store.buildingData[buildingId.value]?.category : 'community',
    bedroom_number: formatBedrooms(Store.allUnitCardData[unitId.value].bedroom),
    unit_size: Store.allUnitCardData[unitId.value].measurement,
    measurement_type: Store.allUnitCardData[unitId.value].measurement_type,
    unit_price: Store.allUnitCardData[unitId.value].price,
    currency_type: Store.allUnitCardData[unitId.value].currency,
    availabilty_tag: Store.allUnitCardData[unitId.value].status,
    // unit_timespent: timespent.value,
    // unit_timestart: startTime.value,
  };
  return fields;
};

function gotoUnit () {
  var endtime = new Date();
  var difference = Math.abs(startTime.value - endtime) / 1000;
  timespent.value = difference;
  startTime.value = new Date();
  let ga_unit_clicked = buildAnalyticsFields();
  ga_unit_clicked ={
    ...ga_unit_clicked,
    unit_id: unitId.value,
    unit_name: Store.allUnitCardData[unitId.value]?.name,
    unitplan_id: unitplanId.value,
  };
  // add floor_id if exists
  if (route.query.floor_id){
    ga_unit_clicked ={
      ...ga_unit_clicked,
      floor_id: route.query.floor_id,
    };
  }
  if (buildingId.value) {
    ga_unit_clicked = {
      ...ga_unit_clicked,
      building_name: Store.buildingData[buildingId.value].name,
      building_id: buildingId.value,
    };
  }
  Googleanalytics("unit_clicked", ga_unit_clicked);
  Store.selectedTabOption = 'floor plan';
  let ga_fields = buildAnalyticsFields();
  ga_fields={
    ...ga_fields,
    unitplan_id: unitplanId.value,
    unitplan_name: Store.unitplanData[unitplanId.value]?.name,
  };
  Googleanalytics('unitplan_clicked', ga_fields);
  router.push({ name: "unit.Child", params: { unitplanId: unitplanId.value }, query: { ...route.query, unit_id: unitId.value, type: "unitplan", unit_name: Store.allUnitCardData[unitId.value].name } });
}

// Watch(()=>{},route.query.floor_id,(new_floor_id)=>{
// SetRenderData(new_floor_id);
// })
function enterVR () {
  let ga_unit_clicked = buildAnalyticsFields();

  if (route.query.floor_id){
    ga_unit_clicked ={
      ...ga_unit_clicked,
      floor_id: route.query.floor_id,
    };
  }

  if (buildingId.value) {
    ga_unit_clicked = {
      ...ga_unit_clicked,
      building_name: Store.buildingData[buildingId.value].name,
      building_id: buildingId.value,
    };
  }
  Googleanalytics("unit_clicked", ga_unit_clicked);

  let ga_fields = buildAnalyticsFields();
  if (Store.unitplanData[unitplanId.value].exterior_type) {
    ga_fields ={
      ...ga_fields,
      unit_id: unitId.value,
      unit_name: Store.allUnitCardData[unitId.value]?.name,
    };
    Googleanalytics("exterior_clicked", ga_fields );
    router.push({ name: "unit.Child", params: { unitplanId: unitplanId.value }, query: { ...route.query, unit_id: unitId.value, type: "exterior", unit_name: Store.allUnitCardData[unitId.value].name } });
  } else {
    ga_fields={
      ...ga_fields,
      tour_id: Store.unitplanData[unitplanId.value].tour_id? Store.unitplanData[unitplanId.value]?.tour_id:Store.unitplanData[unitplanId.value]?.tour_id,
      tour_name: Store.unitplanData[unitplanId.value].name? Store.unitplanData[unitplanId.value]?.name:Store.unitplanData[unitplanId.value]?.name,
    };
    Googleanalytics("tour_clicked", ga_fields);
    router.push({ name: "unit.Child", params: { unitplanId: unitplanId.value }, query: { ...route.query, unit_id: unitId.value, type: "interior", unit_name: Store.allUnitCardData[unitId.value].name } });
  }
}
watch(() => Store.currentZoomlevel, () => {
  setActiveElem(unitSVGElem.value, Store.currentZoomlevel);
  setActiveElem(unitLabelElem.value, Store.currentZoomlevel);
});
watch(() => Store.unitData, () => {
  setRenderData(route.query.floor_id).then(() => {
    if (props.sceneType === 'deep_zoom') {
      setSVG();
    }
    if (!showSVG.value){
      showSVG.value = true;
      setTimeout(() => {
        showLabel.value=true;
      }, 100);
    }
  });
});
watch(() => Store.svgVisibility.showSVG, async (val) => {
  if (val && props.sceneType !== 'deep_zoom') {
    showLabel.value = false;
    await nextTick();
    showLabel.value = true;
  }
});
function handleSelection (floor_id) {

  const scene_data = Store.SceneData[route.params.sceneId].sceneData;

  if (scene_data.floor_ids.includes(String(floor_id))) {
    if (Store.floorData[buildingId.value][floor_id]) {
      showSVG.value = false;
      setTimeout(() => {
        setRenderData(floor_id.toString()).then(() => {
          if (props.sceneType === 'deep_zoom') {
            Store.deleteOverlays();
            setSVG();
          }
          showSVG.value = true;
          router.push({ name: "projectScene", query: { ...route.query, floor_id: floor_id } });
        });
      }, 100);
    }
  } else {
    const parentSceneId = Store.SceneData[route.params.sceneId].sceneData.parent;
    const svg_data = Object.values(Store.SceneData[parentSceneId].svgData).filter((item) => item.type === 'floor');
    svg_data.forEach((item) => {
      const layersArray = Object.values(item.layers);
      const layer = layersArray.find((layer) => layer.floor_id && layer.floor_id.toString() === floor_id.toString() && layer.building_id === route.query.building_id && layer.type === 'floor');
      if (layer !== undefined && layer !== null) {
        router.push({ name: "projectScene", params: { 'sceneId': layer.scene_id }, query: { ...route.query, floor_id: floor_id } });
      } else {
        Store.getProjectSceneId(route.params.projectId, route.params.organizationId, floor_id).then((sceneId) => {
          router.push({ name: "projectScene", params: { 'sceneId': sceneId }, query: { ...route.query, floor_id: floor_id } });
        });
      }
    });
  }
}

// Watch(()=>{},route.query.floor_id,(new_floor_id)=>{
// SetRenderData(new_floor_id);
// })
onClickOutside(filterBar, () => closeFilterModal(), { ignore: [filterBarBtn, toastRef] });

onMounted(() => {
  if (Object.keys(route.query).length > 0){
    const queryParams = createQueryParams(route.query);
    handleResults(queryParams);
  }
  isAndriod.value = /Android/i.test(navigator.userAgent);
});
onUnmounted(() => {
  Store.showFilter = false;
  window.removeEventListener("popstate", () => { });
});
onClickOutside(list, (event) => {
  if (!document.getElementById(clickedId.value).contains(event.target)) {
    if (props.sceneType === 'deep_zoom') {
      const unitElem = unitSVGElem.value[clickedId.value].g;
      unitElem.classList.add('opacity-30');
      unitElem.classList.remove('transparent', 'opacity-70', '!fill-[#ffffff00]');
      const classes = getUnitData(rendered_layerData.value[clickedId.value].unit);
      const importantClasses = classes.split(' ');
      importantClasses.forEach((className) => {
        unitElem.classList.add(className);
      });
    }
    showUnitCard.value = false;
    clickedId.value = false;

  }
});

if (props.sceneType==="deep_zoom"){
  window.viewer.addHandler('zoom', function () {
    closeModal();
  });
}

watch(() => Store.svgVisibility.showSVG, (isVisible) => {
  if (props.sceneType !== 'deep_zoom') {
    return;
  }
  Object.values(unitSVGElem.value).forEach((elem) => {
    if (elem && elem.g) {
      if (isVisible) {
        elem.g.classList.remove('!hidden');
      } else {
        elem.g.classList.add('!hidden');
      }
    }
  });
  Object.values(unitLabelElem.value).forEach((elem) => {
    if (elem && elem.g) {
      if (isVisible) {
        elem.g.classList.remove('!hidden');
      } else {
        elem.g.classList.add('!hidden');
      }
    }
  });
});
</script>
<template>
  <template v-if="Store.svgVisibility.showSVG">
    <g
      v-for="(layer, index) in rendered_layerData"
      v-show="showSVG && props.sceneType !== 'deep_zoom'"
      :id="layer.layer_data.layer_id"
      :key="index"
      ref="unirefs"
      style="  fill-opacity: 0.4;"
      :class="props.sceneType !== 'deep_zoom' ? [
        'cursor-pointer',
        route.params.organizationId+' '+rendered_layerData[layer.layer_data.layer_id].unit.status,
        'hover:transparent hover:opacity-70   ',
        layer.layer.getAttribute('class') + ' ' + layer.layer_data.type,
        clickedId == layer.layer_data.layer_id && clickedId != false ? '!fill-[#ffffff00]' : 'bg-black',
        rendered_layerData[layer.layer_data.layer_id].unit ? getUnitData(rendered_layerData[layer.layer_data.layer_id].unit) : 'fill-none',
      ] : ''"
      :label="rendered_layerData[index]['unit'] ? rendered_layerData[index]['unit'].name : ''"
      :status="rendered_layerData[layer.layer_data.layer_id].unit.status"
      @click="(event) => { moveToNxtScene(layer.layer_data.layer_id, rendered_layerData[index]['unit']._id, event) }"
      v-html="layer.layer.innerHTML"
    />
  </template>
  <portal to="units">
    <FloorSwitch
      v-if="Store.allBuildings && Store.enabledFloors && route.query.floor_id && !Store.isLandscape && Store.svgVisibility.showSVG"
      class="hidden md:flex"
      :number-of-floors="sortedFloors"
      :enabled-floors="Store.enabledFloors"
      per-slide-view="3"
      :default-id="route.query.floor_id"
      @handle-selection="handleSelection"
    />

    <div
      v-if="Store.allBuildings && Store.enabledFloors && route.query.floor_id && Store.svgVisibility.showSVG"
      class=" fixed h-fit z-[3] w-[2.12rem]"
      :class="Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0? 'right-32 -bottom-6 rotate-90': Store.isLandscape? 'right-12 -bottom-6 rotate-90':'top-[34%] md:hidden right-0'"
    >
      <button
        :onclick="() => floorsMobileViewToggle = !floorsMobileViewToggle"
        type="button"
        :class="[(floorsMobileViewToggle ? '' : 'rounded-bl-lg'), 'flex flex-col justify-center bg-secondary text-secondaryText gap-5 py-4 items-center bg-opacity-60 rounded-tl-lg backdrop-blur-[80px]']"
      >
        <span class="-rotate-90">
          <TranslationComp text="Floor" />
        </span>
        <svg
          :class="[(floorsMobileViewToggle ? (Store.isLandscape ? 'rotate-0' : 'rotate-0') : (Store.isLandscape ? 'rotate-180' : 'rotate-180')), 'fill-secondaryText w-[6px] h-3 transition-transform duration-500 ease-[cubic-bezier(0.165, 0.84, 0.44, 1)]']"
          viewBox="0 0 6 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M0.856844 12C0.63735 12 0.418064 11.9026 0.25109 11.7068C-0.0836966 11.316 -0.0836966 10.6827 0.25109 10.2921L3.93081 5.99927L0.25109 1.70762C-0.0836966 1.31679 -0.0836966 0.683538 0.25109 0.292945C0.585876 -0.0976483 1.12865 -0.0976483 1.46364 0.292945L5.74891 5.29254C6.0837 5.68313 6.0837 6.31638 5.74891 6.70722L1.46364 11.7068C1.29562 11.9026 1.07613 12 0.856844 12Z"
            fill=""
          />
        </svg>
      </button>

      <FloorSwitchSlider
        v-if="Store.allBuildings && Store.enabledFloors && route.query.floor_id"
        :class="[(floorsMobileViewToggle ? (Store.isLandscape ? '-left-[109px] -top-[27px] -rotate-90' : 'right-0' ): '-right-[200px] '),(Store.isLandscape ? 'w-[5.48rem]':'w-[2.12rem]' ), 'fixed h-fit transition-[right] duration-500 ease-[cubic-bezier(0.165, 0.84, 0.44, 1)']"
        :number-of-floors="sortedFloors"
        :default-id="route.query.floor_id"
        :enabled-floors="Store.enabledFloors"
        :per-slide-view="Store.isLandscape ? 3: 5"
        @handle-selection="handleSelection"
      />
    </div>

    <div
      v-if="!Store.isMobile && route.query.floor_id"
      class="absolute bottom-8 right-8"
    >
      <FloorPlateCard
        v-if="Store.floorData[buildingId]"
        :title="Store.floorData[buildingId][route.query.floor_id].floors"
        :number-of-units="Store.floorData[buildingId][route.query.floor_id].units"
        :measurement-min="Store.floorData[buildingId][route.query.floor_id].minArea"
        :measurement-max="Store.floorData[buildingId][route.query.floor_id].maxArea"
        :measurement-type="Store.floorData[buildingId][route.query.floor_id].measurementType"
        :bedrooms-min="Store.floorData[buildingId][route.query.floor_id].minBedrooms"
        :bedrooms-max="Store.floorData[buildingId][route.query.floor_id].maxBedrooms"
        :available-status="Store.floorData[buildingId][route.query.floor_id].status"
        :available-units="Store.floorData[buildingId][route.query.floor_id].totalAvailableUnits"
        :hideStatus="Store.hideStatus"
      />
    </div>
    <div
      v-if="showUnitCard"
      ref="list"
      class="flex fixed"
      :style="!Store.isMobile && !Store.isLandscape ? { 'top': position.y + 'px', 'left': position.x + 'px' } : ''"
      :class="[Store.isMobile ? '-bottom-20 z-10' : 'z-[9]', Store.isTouchScreen ? 'h-fit sm:h-full' : '' , (Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0)?'!bottom-4 h-fit !right-24 z-10': Store.isLandscape?'!bottom-3 h-fit !right-4  z-10':'']"
    >
      <FloorPlanCard
        class="top-auto right-auto"
        :title="Store.allUnitCardData[unitId].name"
        :community="route.query.community_id ? Store.communityData[route.query.community_id].name : Store.buildingData[buildingId].name"
        :status="Store.allUnitCardData[unitId].status"
        :price="Store.allUnitCardData[unitId].price"
        :maxPrice="Store.allUnitCardData[unitId].max_price"
        :measurement="Store.allUnitCardData[unitId].measurement"
        :measurement-type="Store.allUnitCardData[unitId].measurement_type"
        :bedrooms="Store.allUnitCardData[unitId].bedroom"
        :bathrooms="Store.allUnitCardData[unitId].bathroom"
        :currency-title="Store.allUnitCardData[unitId].currency"
        :is-show-buttons="false"
        :styleType="Store.unitplanData[Store.allUnitCardData[unitId].unitplan_id].style ? Store.unitplanData[Store.allUnitCardData[unitId].unitplan_id].style : null"
        :type="route.query.community_id ? Store.communityData[route.query.community_id].category : ''"
        :button-view="route.query.community_id ? Store.communityData[route.query.community_id].category === 'villa' ? 'villaview' : 'floorplanview' : 'floorplanview'"
        :isMobile="Store.isMobile"
        :isTouchScreen="Store.isTouchScreen"
        :favUnits="Store.favoritesData"
        :unitId="unitId"
        :hideStatus="Store.hideStatus"
        :hideClose="false"
        :hide-enter-vr="(Store.unitData[unit] && Store.unitData[unitId].tour_id === null || Store.unitData[unitId].tour_id === undefined || Store.unitData[unitId].tour_id === '') && (Store.unitplanData && Store.unitplanData[Store.unitData[unitId].unitplan_id].tour_id === null || Store.unitplanData[Store.unitData[unitId].unitplan_id].tour_id === undefined || Store.unitplanData[Store.unitData[unitId].unitplan_id].tour_id === '') ? true : false"
        :is_commercial="Store.unitplanData[Store.allUnitCardData[unitId].unitplan_id]?.is_commercial"
        @show-unitplan="gotoUnit"
        @close-modal="closeModal"
        @enter-v-r="enterVR"
        @handle-favorite="Store.updateAddRemovefavorites(Store.allUnitCardData[unitId])"
      />
    </div>
    <div v-if="Store.projectCardData[route.params.projectId].projectSettings.ale.unitcard_config.bedrooms && Store.svgVisibility.showSVG">
      <BedroomLegend
        v-if="showSVG"
        :unitData="currentUnits"
      />
    </div>
    <div v-if="showLabel && Store.svgVisibility.showSVG">
      <div
        v-for="unit, unitIndex in unirefs"
        v-show="props.sceneType !== 'deep_zoom'"
        :id="unit.getAttribute('id') + 'label'"
        :key="unitIndex"
        class="fixed top-0 left-0 z-0 pointer-events-none  lg:block opacity-75"
        :style="{ 'top': findCenterOfDiv(unit.getBoundingClientRect()).top + 'px', 'left': findCenterOfDiv(unit.getBoundingClientRect()).left + 'px' }"
      >
        <span
          class=" bg-black -z-10 bg-opacity-10 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 truncate "
          :class="route.params.organizationId+' '+unit.getAttribute('status')"
        >
          <UnitLabel
            v-if="unit.getAttribute('label') && on_pan"
            :label="unit.getAttribute('label')"
          />
        </span>
      </div>
    </div>

    <div
      v-if="!emptyFilterBar && Store.svgVisibility.showSVG"
      class="absolute z-[4] "
      :class="[Store.isMobile ? 'flex flex-col gap-6 right-[0.8rem]' : Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0 ? 'right-[5.5rem] !z-[3]' : Store.isLandscape ? 'right-[1rem]': 'right-4 bottom-0 ', Store.isMobile && !isAndriod ? 'top-[5.5rem]' : Store.isLandscape? 'top-[3.5rem]': 'top-[5.5rem]']"
    >
      <!-- Filter Button for Desktop -->
      <FilterButton
        v-if="!Store.isMobile && !Store.isLandscape"
        ref="filterBarBtn"
        message="Filters"
        :isToggle="filterBtnToggle"
        @handle-button-click="showModal"
      />

      <!-- Control Button for Mobile -->
      <ControlButton
        v-if="Store.showFilter && (Store.isMobile || Store.isLandscape)"
        :is-hover="showTooltip.includes('filter')"
        :inactive-s-v-g="toolBtnData.filterInactiveSVG"
        :active-s-v-g="toolBtnData.filterActiveSVG"
        class="!m-0 !filterbutton"
        :class="isMobile ? 'h-10 w-10 p-2' : ''"
        tooltiptext="Apply Filters"
        @on-click="onClickButton(&quot;filter&quot;)"
        @mouseout="removeItem('filter')"
      />

      <!-- Unified FilterBar Component -->
      <div
        class=" fixed bottom-0 right-0 !z-[9]"
        :class="Store.isMobile ? 'w-full bottom-[3.8rem]': Store.isLandscape && Store.sidebarOptions && Object.keys(Store.sidebarOptions[route.params.projectId] || {}).length > 0 ? 'w-full right-[5rem]' : Store.isLandscape? 'w-full right-0':''"
      >
        <FilterBar
          ref="filterBar"
          :openModal="isShowModal || openFilterModal"
          :showStatusFilter="Store.hideStatus"
          @close-modal="closeFilterModal"
          @show-results="handleResults"
          @clear-filter="() => {
            if (clearFilterRef) {
              handleClearOnError()
            } else {
              handleClear()
            }}"
          @close-on-error="handleSearchError"
          @empty-filter="handleEmptyFilter"
        />
      </div>

      <!-- Unified Toast Component -->
      <Toast
        v-if="searchResultToast"
        position="bottom-center"
        size="lg"
        :autoClose="true"
        :duration="5"
        @update:show-toast="closeToast"
      >
        <template #content>
          <div class="h-7 flex items-center gap-4 toastSVG">
            <span class="w-5">
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="information-circle">
                  <path
                    id="Vector"
                    d="M22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22C17.5228 22 22 17.5228 22 12Z"
                    stroke="white"
                    stroke-width="1.5"
                  />
                  <path
                    id="Vector_2"
                    d="M12.2422 17V12C12.2422 11.5286 12.2422 11.2929 12.0957 11.1464C11.9493 11 11.7136 11 11.2422 11"
                    stroke="white"
                    stroke-width="1.5"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    id="Vector_3"
                    d="M11.9922 8H12.0012"
                    stroke="white"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </g>
              </svg>
            </span>
            <span
              class="text-secondaryText pl-4 border-l border-gray-200 text-base font-normal font-['Inter'] leading-normal "
            >No Units Available</span>
          </div>
        </template>
      </Toast>
    </div>
  </portal>
</template>
<style scoped>
.invisible{
  opacity:0;
}

.filterbutton button div svg path{
  fill:red !important;
}
.toastSVG svg path{
  stroke:red !important;
}
.yRnIS3.sold,
.yRnIS3.onhold,
.yRnIS3.reserved {
  display: none !important;
}

</style>
