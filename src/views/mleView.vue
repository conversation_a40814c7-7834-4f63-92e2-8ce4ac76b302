<script setup>
import <PERSON><PERSON><PERSON>iewer from './MLEViewer.vue';
import { creationToolStore } from '../store';
import { computed } from 'vue';
import { cdn } from '../helpers/helper';

const props = defineProps({
  projectId: {
    type: String,
    default: "",
  },
  organizationId: {
    type: String,
    default: "",
  },
  tourId: {
    type: String,
    default: "",
  },
});
const Store = creationToolStore();
const storagePath = `https://${import.meta.env.VITE_APP_BUCKET_CDN}/CreationtoolAssets/${props.organizationId}/projects/${props.projectId}/tours/${props.tourId}`;

// Computed property for VR data
const vrData = computed(() => {
  return Store.listOfVrTourData?.[props.tourId] || {};
});

// Computed property for filtered images with updated_at
const filteredImages = computed(() => {
  if (!vrData.value || !vrData.value.images) {
    return {};
  }

  const result = {};

  Object.keys(vrData.value.images).forEach((key) => {
    const imageData = vrData.value.images[key];
    if (imageData && imageData.updated_at && imageData.name) {
      // Remove file extension from the name
      const nameWithoutExtension = imageData.name.replace(/\.[^/.]+$/, "");
      result[nameWithoutExtension] = {
        updated_at: imageData.updated_at,
      };
    }
  });

  return result;
});

// Load VR data
Store.getListOfVRId(props.organizationId, props.projectId);
Store.getTranslation(props.organizationId);
Store.getOrganization(props.organizationId);
Store.getListofProjects(props.organizationId, props.projectId);
window.addEventListener('resize', Store.callbackFunctionMonitorChanges);
Store.callbackFunctionMonitorChanges();
</script>
<template>
  <div class="relative h-full w-full">
    <MLEViewer
      v-if="Object.keys(vrData).length"
      :modelURL="cdn(vrData.model)"
      :cameraURL="cdn(vrData.camera)"
      :storagePath="storagePath"
      :initialRotation="vrData.initial_rotation"
      :tourLabels="vrData.labels"
      :isMobile="Store.isMobile"
      :filteredImages="filteredImages"
    />
  </div>
</template>
<style scoped>
</style>
